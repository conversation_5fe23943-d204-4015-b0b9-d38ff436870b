/**
 * Database Service Layer for Feature Flags
 * 
 * Provides comprehensive CRUD operations for feature flags with caching,
 * seeding functionality, and utility methods for database-driven feature management.
 */

import { prisma } from './prisma';
import { FeatureFlag } from '@prisma/client';

// Types for feature flag operations
export interface CreateFeatureFlagInput {
  key: string;
  name: string;
  description?: string;
  enabled?: boolean;
  group: string;
  metadata?: Record<string, unknown>;
  createdBy?: string;
}

export interface UpdateFeatureFlagInput {
  name?: string;
  description?: string;
  enabled?: boolean;
  group?: string;
  metadata?: Record<string, unknown>;
  updatedBy?: string;
}

export interface FeatureFlagWithMetadata extends Omit<FeatureFlag, 'metadata'> {
  metadata?: Record<string, unknown>;
}

// Cache for feature flags (5-minute TTL)
interface CacheEntry {
  data: FeatureFlagWithMetadata[];
  timestamp: number;
}

let cache: CacheEntry | null = null;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Clear the feature flags cache
 */
export function clearFeatureFlagCache(): void {
  cache = null;
}

/**
 * Check if cache is valid
 */
function isCacheValid(): boolean {
  return cache !== null && (Date.now() - cache.timestamp) < CACHE_TTL;
}

/**
 * Parse metadata from JSON string
 */
function parseMetadata(metadata: string | null): Record<string, unknown> | undefined {
  if (!metadata) return undefined;
  try {
    return JSON.parse(metadata);
  } catch {
    return undefined;
  }
}

/**
 * Stringify metadata to JSON
 */
function stringifyMetadata(metadata: Record<string, unknown> | undefined): string | undefined {
  if (!metadata) return undefined;
  try {
    return JSON.stringify(metadata);
  } catch {
    return undefined;
  }
}

/**
 * Transform database record to include parsed metadata
 */
function transformFeatureFlag(flag: FeatureFlag): FeatureFlagWithMetadata {
  return {
    ...flag,
    metadata: parseMetadata(flag.metadata)
  };
}

/**
 * Get the most recent modification timestamp across all feature flags
 */
export async function getLastModificationTime(): Promise<Date | null> {
  try {
    const result = await prisma.featureFlag.findFirst({
      select: { updatedAt: true },
      orderBy: { updatedAt: 'desc' }
    });

    return result?.updatedAt || null;
  } catch (error) {
    console.error('Error getting last modification time:', error);
    return null;
  }
}

/**
 * Get cache timestamp (when cache was last populated)
 */
export function getCacheTimestamp(): Date | null {
  return cache ? new Date(cache.timestamp) : null;
}

/**
 * Get all feature flags from database with caching
 */
export async function getAllFeatureFlags(): Promise<FeatureFlagWithMetadata[]> {
  // Return cached data if valid
  if (isCacheValid() && cache) {
    return cache.data;
  }

  try {
    const flags = await prisma.featureFlag.findMany({
      orderBy: [
        { group: 'asc' },
        { name: 'asc' }
      ]
    });

    const transformedFlags = flags.map(transformFeatureFlag);

    // Update cache
    cache = {
      data: transformedFlags,
      timestamp: Date.now()
    };

    return transformedFlags;
  } catch (error) {
    console.error('Error fetching feature flags from database:', error);
    throw new Error('Failed to fetch feature flags');
  }
}

/**
 * Get feature flag by key
 */
export async function getFeatureFlagByKey(key: string): Promise<FeatureFlagWithMetadata | null> {
  try {
    const flag = await prisma.featureFlag.findUnique({
      where: { key }
    });

    return flag ? transformFeatureFlag(flag) : null;
  } catch (error) {
    console.error(`Error fetching feature flag ${key}:`, error);
    throw new Error(`Failed to fetch feature flag: ${key}`);
  }
}

/**
 * Get feature flags by group
 */
export async function getFeatureFlagsByGroup(group: string): Promise<FeatureFlagWithMetadata[]> {
  try {
    const flags = await prisma.featureFlag.findMany({
      where: { group },
      orderBy: { name: 'asc' }
    });

    return flags.map(transformFeatureFlag);
  } catch (error) {
    console.error(`Error fetching feature flags for group ${group}:`, error);
    throw new Error(`Failed to fetch feature flags for group: ${group}`);
  }
}

/**
 * Create a new feature flag
 */
export async function createFeatureFlag(input: CreateFeatureFlagInput): Promise<FeatureFlagWithMetadata> {
  try {
    const flag = await prisma.featureFlag.create({
      data: {
        key: input.key,
        name: input.name,
        description: input.description,
        enabled: input.enabled ?? false,
        group: input.group,
        metadata: stringifyMetadata(input.metadata),
        createdBy: input.createdBy
      }
    });

    // Clear cache after creation
    clearFeatureFlagCache();

    return transformFeatureFlag(flag);
  } catch (error) {
    console.error('Error creating feature flag:', error);
    throw new Error('Failed to create feature flag');
  }
}

/**
 * Update an existing feature flag
 */
export async function updateFeatureFlag(key: string, input: UpdateFeatureFlagInput): Promise<FeatureFlagWithMetadata> {
  try {
    const flag = await prisma.featureFlag.update({
      where: { key },
      data: {
        ...(input.name !== undefined && { name: input.name }),
        ...(input.description !== undefined && { description: input.description }),
        ...(input.enabled !== undefined && { enabled: input.enabled }),
        ...(input.group !== undefined && { group: input.group }),
        ...(input.metadata !== undefined && { metadata: stringifyMetadata(input.metadata) }),
        ...(input.updatedBy !== undefined && { updatedBy: input.updatedBy })
      }
    });

    // Clear cache after update
    clearFeatureFlagCache();

    return transformFeatureFlag(flag);
  } catch (error) {
    console.error(`Error updating feature flag ${key}:`, error);
    throw new Error(`Failed to update feature flag: ${key}`);
  }
}

/**
 * Toggle a feature flag's enabled state
 */
export async function toggleFeatureFlag(key: string, updatedBy?: string): Promise<FeatureFlagWithMetadata> {
  try {
    // First get the current state
    const currentFlag = await prisma.featureFlag.findUnique({
      where: { key }
    });

    if (!currentFlag) {
      throw new Error(`Feature flag not found: ${key}`);
    }

    // Toggle the enabled state
    const flag = await prisma.featureFlag.update({
      where: { key },
      data: {
        enabled: !currentFlag.enabled,
        ...(updatedBy && { updatedBy })
      }
    });

    // Clear cache after toggle
    clearFeatureFlagCache();

    return transformFeatureFlag(flag);
  } catch (error) {
    console.error(`Error toggling feature flag ${key}:`, error);
    throw new Error(`Failed to toggle feature flag: ${key}`);
  }
}

/**
 * Delete a feature flag
 */
export async function deleteFeatureFlag(key: string): Promise<void> {
  try {
    await prisma.featureFlag.delete({
      where: { key }
    });

    // Clear cache after deletion
    clearFeatureFlagCache();
  } catch (error) {
    console.error(`Error deleting feature flag ${key}:`, error);
    throw new Error(`Failed to delete feature flag: ${key}`);
  }
}

/**
 * Check if a feature flag is enabled
 */
export async function isFeatureFlagEnabled(key: string): Promise<boolean> {
  try {
    const flag = await getFeatureFlagByKey(key);
    return flag?.enabled ?? false;
  } catch (error) {
    console.error(`Error checking feature flag ${key}:`, error);
    return false; // Default to disabled on error
  }
}

/**
 * Get feature flag statistics
 */
export async function getFeatureFlagStats(): Promise<{
  total: number;
  enabled: number;
  disabled: number;
  groups: number;
  enabledPercentage: number;
}> {
  try {
    const flags = await getAllFeatureFlags();
    const enabled = flags.filter(f => f.enabled).length;
    const groups = new Set(flags.map(f => f.group)).size;

    return {
      total: flags.length,
      enabled,
      disabled: flags.length - enabled,
      groups,
      enabledPercentage: flags.length > 0 ? Math.round((enabled / flags.length) * 100) : 0
    };
  } catch (error) {
    console.error('Error getting feature flag stats:', error);
    return {
      total: 0,
      enabled: 0,
      disabled: 0,
      groups: 0,
      enabledPercentage: 0
    };
  }
}

/**
 * Seed default feature flags
 * Creates the standard feature flags that match the environment-based system
 */
export async function seedDefaultFeatureFlags(createdBy?: string): Promise<FeatureFlagWithMetadata[]> {
  const defaultFlags: CreateFeatureFlagInput[] = [
    // Authentication Providers (disabled by default - require admin to enable)
    {
      key: 'google_oauth',
      name: 'Google OAuth',
      description: 'Google OAuth authentication provider',
      enabled: false,
      group: 'auth_providers',
      metadata: {
        provider: 'google',
        icon: 'GoogleIcon',
        component: 'GoogleSignIn'
      },
      createdBy
    },
    {
      key: 'github_oauth',
      name: 'GitHub OAuth',
      description: 'GitHub OAuth authentication provider',
      enabled: false,
      group: 'auth_providers',
      metadata: {
        provider: 'github',
        icon: 'GitHubIcon',
        component: 'GitHubSignIn'
      },
      createdBy
    },
    {
      key: 'amazon_oauth',
      name: 'Amazon OAuth',
      description: 'Amazon OAuth authentication provider',
      enabled: false,
      group: 'auth_providers',
      metadata: {
        provider: 'amazon',
        icon: 'AmazonIcon',
        component: 'AmazonSignIn'
      },
      createdBy
    },
    {
      key: 'email_magic_links',
      name: 'Email Magic Links',
      description: 'Passwordless email authentication',
      enabled: false,
      group: 'auth_providers',
      metadata: {
        provider: 'email',
        icon: 'EmailIcon',
        component: 'EmailButton'
      },
      createdBy
    },
    // UI Components (disabled by default - require admin to enable)
    {
      key: 'footer',
      name: 'Footer Component',
      description: 'Site footer with links and information',
      enabled: false,
      group: 'ui_components',
      metadata: {
        component: 'Footer',
        location: 'global'
      },
      createdBy
    },
    // API Endpoints
    {
      key: 'debug_session_api',
      name: 'Debug Session API',
      description: 'API endpoint for session debugging',
      enabled: false, // Disabled by default for security
      group: 'api_endpoints',
      metadata: {
        endpoint: '/api/debug-session',
        method: 'GET',
        requiresAuth: true
      },
      createdBy
    },
    {
      key: 'session_cleanup_api',
      name: 'Session Cleanup API',
      description: 'API endpoint for manual session cleanup',
      enabled: false, // Disabled by default - require admin to enable
      group: 'api_endpoints',
      metadata: {
        endpoint: '/api/cleanup-sessions',
        method: 'POST',
        requiresAuth: true
      },
      createdBy
    }
  ];

  try {
    const createdFlags: FeatureFlagWithMetadata[] = [];

    for (const flagInput of defaultFlags) {
      // Check if flag already exists
      const existing = await getFeatureFlagByKey(flagInput.key);

      if (!existing) {
        const created = await createFeatureFlag(flagInput);
        createdFlags.push(created);
      } else {
        createdFlags.push(existing);
      }
    }

    return createdFlags;
  } catch (error) {
    console.error('Error seeding default feature flags:', error);
    throw new Error('Failed to seed default feature flags');
  }
}

/**
 * Bulk update feature flags
 */
export async function bulkUpdateFeatureFlags(
  updates: Array<{ key: string; enabled: boolean }>,
  updatedBy?: string
): Promise<FeatureFlagWithMetadata[]> {
  try {
    const updatedFlags: FeatureFlagWithMetadata[] = [];

    for (const update of updates) {
      const flag = await updateFeatureFlag(update.key, {
        enabled: update.enabled,
        updatedBy
      });
      updatedFlags.push(flag);
    }

    return updatedFlags;
  } catch (error) {
    console.error('Error bulk updating feature flags:', error);
    throw new Error('Failed to bulk update feature flags');
  }
}
