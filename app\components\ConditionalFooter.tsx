'use client';

import { useUIComponentFlags } from '@/app/hooks/useClientFeatureFlags';
import Footer from './Footer';

export default function ConditionalFooter() {
  const { isFooterEnabled, isLoading, error } = useUIComponentFlags();

  // Don't render anything while loading to prevent layout shift
  if (isLoading) {
    return null;
  }

  // Don't render footer if there's an error (fail closed)
  if (error) {
    console.error('Failed to load footer feature flag:', error);
    return null;
  }

  // Only render footer if the feature flag is enabled
  if (!isFooterEnabled) {
    return null;
  }

  return <Footer />;
}
