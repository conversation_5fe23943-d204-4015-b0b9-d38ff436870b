import { getServerSession } from 'next-auth';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import { redirect } from 'next/navigation';

import LogoutButton from '@/app/components/LogoutButton';
import ProviderIcon from '@/app/components/ProviderIcon';
import HybridFeatureManagement from '@/app/components/HybridFeatureManagement';
import { formatProviderName } from '@/app/lib/utils';

export default async function DashboardPage() {
  // Get session using NextAuth with hybrid configuration
  const session = await getServerSession(getHybridAuthOptionsForSession());

  // Redirect to signin if not authenticated
  if (!session?.user) {
    redirect('/signin');
  }

  const user = session.user;

  return (
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Dashboard</h1>
              <div className="flex items-center">
                <LogoutButton />
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              <h2 className="text-lg font-medium mb-2">Your Profile</h2>
              <ul className="space-y-2">
                {user.name && (
                  <li><strong>Name:</strong> {user.name}</li>
                )}
                {user.email && (
                  <li className="break-all sm:break-normal"><strong>Email:</strong> {user.email}</li>
                )}

                {user.emailVerified && (
                  <li><strong>Email Verified:</strong> {user.emailVerified.toLocaleDateString()}</li>
                )}
                {user.provider && (
                  <li className="flex items-center space-x-2 flex-wrap sm:flex-nowrap">
                    <strong>Sign-in Provider:</strong>
                    <div className="flex items-center space-x-2">
                      <ProviderIcon
                        provider={user.provider}
                        size={16}
                        className="text-gray-600"
                      />
                      <span>{formatProviderName(user.provider)}</span>
                    </div>
                  </li>
                )}
              </ul>
            </div>

            {/* Hybrid Feature Management Section */}
            <div className="mt-8">
              <HybridFeatureManagement compactMode={true} showSeedButton={false} />
            </div>
          </div>
        </div>
      </div>
  );
}
