import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { CustomPrismaAdapter } from './prisma-adapter';
import { sendVerificationRequest } from './email';
import { isAuthProviderEnabled } from './feature-config';



const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';
const amazonClientId = process.env.AMAZON_CLIENT_ID || '';
const amazonClientSecret = process.env.AMAZON_CLIENT_SECRET || '';
const resendApiKey = process.env.RESEND_API_KEY || '';
const resendSender = process.env.RESEND_SENDER || '';

// Custom Amazon OAuth Provider
const AmazonProvider = {
  id: 'amazon',
  name: 'Amazon',
  type: 'oauth' as const,
  authorization: {
    url: 'https://www.amazon.com/ap/oa',
    params: {
      scope: 'profile',
      response_type: 'code',
    },
  },
  token: 'https://api.amazon.com/auth/o2/token',
  userinfo: 'https://api.amazon.com/user/profile',
  clientId: amazonClientId,
  clientSecret: amazonClientSecret,
  profile(profile: { user_id: string; name: string; email: string }) {
    return {
      id: profile.user_id,
      name: profile.name,
      email: profile.email,
      // Explicitly exclude profile image
    };
  },
};

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

if (!amazonClientId || !amazonClientSecret) {
  throw new Error('Missing Amazon OAuth credentials');
}

if (!resendApiKey || !resendSender) {
  throw new Error('Missing Resend email credentials');
}

// Build providers array based on feature flags
function buildProviders() {
  const providers = [];

  // Add Google OAuth if enabled
  const googleEnabled = isAuthProviderEnabled('google');
  if (googleEnabled) {
    providers.push(GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          // Explicitly exclude profile image
        };
      },
    }));
  }

  // Add GitHub OAuth if enabled
  const githubEnabled = isAuthProviderEnabled('github');
  if (githubEnabled) {
    providers.push(GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
      profile(profile) {
        return {
          id: profile.id.toString(),
          name: profile.name || profile.login,
          email: profile.email,
          // Explicitly exclude profile image
        };
      },
    }));
  }

  // Add Amazon OAuth if enabled
  const amazonEnabled = isAuthProviderEnabled('amazon');
  if (amazonEnabled) {
    providers.push(AmazonProvider);
  }

  // Add Email provider if enabled
  const emailEnabled = isAuthProviderEnabled('email');
  if (emailEnabled) {
    providers.push(EmailProvider({
      server: {
        host: 'smtp.resend.com',
        port: 587,
        auth: {
          user: 'resend',
          pass: resendApiKey,
        },
      },
      from: resendSender,
      sendVerificationRequest,
      // Extend magic link expiration to 24 hours
      maxAge: 24 * 60 * 60, // 24 hours in seconds
      // Ensure proper URL generation for development
      generateVerificationToken: undefined, // Use default
    }));
  }

  return providers;
}

export const authOptions: NextAuthOptions = {
  adapter: CustomPrismaAdapter(),
  // Enable debug mode to see more detailed logs
  debug: process.env.NODE_ENV === 'development',
  providers: buildProviders(),
  session: {
    // Using JWT strategy: sessions stored in tokens, not database
    // This means the Session table in Prisma schema is unused (kept for future flexibility)
    // To switch to database sessions, change to: strategy: 'database'
    strategy: 'jwt',
    // Extend session duration to 30 days
    maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
    // Update session every 24 hours
    updateAge: 24 * 60 * 60, // 24 hours in seconds
  },
  pages: {
    signIn: '/signin',
    error: '/signin',
    verifyRequest: '/passwordless', // Redirect here after email is sent
  },
  // Ensure proper URL handling
  useSecureCookies: process.env.NODE_ENV === 'production',
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.session-token' : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    }
  },
  callbacks: {
    async signIn({ account, profile }) {

      // Manual account linking for OAuth providers
      if (account?.type === 'oauth' && profile?.email) {
        try {
          // Import prisma here to avoid circular dependencies
          const { prisma } = await import('./prisma');

          // Check if user exists with this email
          const existingUser = await prisma.user.findUnique({
            where: { email: profile.email },
            include: { accounts: true }
          });

          if (existingUser) {
            // Check if this provider account already exists
            const existingAccount = existingUser.accounts.find(
              acc => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId
            );

            if (!existingAccount) {
              // Link the new provider account to the existing user
              await prisma.account.create({
                data: {
                  userId: existingUser.id,
                  type: account.type,
                  provider: account.provider,
                  providerAccountId: account.providerAccountId,
                  access_token: account.access_token,
                  token_type: account.token_type,
                  scope: account.scope,
                  refresh_token: account.refresh_token,
                  expires_at: account.expires_at,
                  id_token: account.id_token,
                  session_state: account.session_state,
                }
              });
            }
          }
        } catch (error) {
          console.error('Manual account linking failed:', error);
          return false;
        }
      }

      return true;
    },
    async jwt({ token, user, account }) {
      // Include user info in JWT token on sign in
      if (user) {
        token.id = user.id;
        token.provider = account?.provider;
      }
      return token;
    },
    async session({ session, token }) {
      // Include user info from JWT token in session
      if (session.user && token) {
        session.user.id = token.id as string;
        session.user.provider = token.provider as string;
      }
      return session;
    },
    async redirect({ baseUrl }) {
      // Always redirect to dashboard after successful authentication
      return `${baseUrl}/dashboard`;
    },
  },
};
