/**
 * Hybrid Feature Management Component
 * 
 * Displays feature flag status with hybrid system support,
 * showing both database and environment sources with admin controls.
 */

'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useHybridFeatureFlags } from '@/app/hooks/useHybridFeatureFlags';
import { useFeatureFlagManagement } from '@/app/hooks/useFeatureFlagManagement';
import Link from 'next/link';

interface HybridFeatureManagementProps {
  compactMode?: boolean;
  showAdminControls?: boolean;
  showSeedButton?: boolean;
}

export default function HybridFeatureManagement({
  compactMode = false,
  showAdminControls = true,
  showSeedButton = true
}: HybridFeatureManagementProps) {
  const { data: session } = useSession();
  const { 
    featureFlags, 
    isLoading, 
    error, 
    refresh, 
    getStats,
    source,
    lastUpdated 
  } = useHybridFeatureFlags(true, 30000); // Auto-refresh every 30 seconds

  const {
    isSeeding,
    seedError,
    seedDefaultFlags,
    clearErrors
  } = useFeatureFlagManagement();

  const [showDetails, setShowDetails] = useState(!compactMode);

  // Handle seed operation
  const handleSeed = async () => {
    const success = await seedDefaultFlags();
    if (success) {
      await refresh();
    }
  };

  // Get statistics
  const stats = getStats();

  if (isLoading && !featureFlags) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">Loading hybrid feature flags...</span>
        </div>
      </div>
    );
  }

  if (error && !featureFlags) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Feature Flags</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button
                onClick={refresh}
                className="mt-2 text-sm text-red-800 underline hover:text-red-900"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Hybrid Feature Flags
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Real-time feature flag status from {source} system
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {!compactMode && (
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-sm text-blue-600 hover:text-blue-500 cursor-pointer"
              >
                {showDetails ? 'Hide Details' : 'Show Details'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-xs text-gray-500">Total Flags</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.enabled}</div>
              <div className="text-xs text-gray-500">Enabled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.disabled}</div>
              <div className="text-xs text-gray-500">Disabled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.enabledPercentage}%</div>
              <div className="text-xs text-gray-500">Enabled</div>
            </div>
          </div>
          
          {/* Enhanced stats for hybrid system */}
          {(stats.databaseFlags !== undefined || stats.environmentFlags !== undefined) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-4">
                {stats.databaseFlags !== undefined && (
                  <div className="text-center">
                    <div className="text-lg font-semibold text-yellow-600">{stats.databaseFlags}</div>
                    <div className="text-xs text-gray-500">Database Flags</div>
                  </div>
                )}
                {stats.environmentFlags !== undefined && (
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-600">{stats.environmentFlags}</div>
                    <div className="text-xs text-gray-500">Environment Flags</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Feature Groups */}
      {showDetails && featureFlags && (
        <div className="px-6 py-4">
          <div className="space-y-4">
            {featureFlags.groups.map((group) => (
              <div key={group.key} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">{group.name}</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                  {Object.entries(group.features).map(([key, enabled]) => (
                    <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-700 truncate">
                        {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {enabled ? 'ON' : 'OFF'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Admin Controls */}
      {showAdminControls && session?.user?.email && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/feature-flags"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Admin Panel
              </Link>
              
              {showSeedButton && (
                <button
                  onClick={handleSeed}
                  disabled={isSeeding}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
                >
                  {isSeeding ? (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  )}
                  Seed Defaults
                </button>
              )}
            </div>

            {/* Error Display */}
            {seedError && (
              <div className="flex items-center space-x-2 text-sm text-red-600">
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span>{seedError}</span>
                <button
                  onClick={clearErrors}
                  className="text-red-800 underline hover:text-red-900 cursor-pointer"
                >
                  Dismiss
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
        <div className="flex justify-between items-center">
          <span>
            Source: <span className="font-medium">{source}</span>
          </span>
          <span>
            {featureFlags?.dataLastModified && featureFlags?.cacheTimestamp ? (
              <>Data modified: {new Date(featureFlags.dataLastModified).toLocaleString()} • Cached: {new Date(featureFlags.cacheTimestamp).toLocaleString()}</>
            ) : featureFlags?.dataLastModified ? (
              <>Data modified: {new Date(featureFlags.dataLastModified).toLocaleString()}</>
            ) : featureFlags?.cacheTimestamp ? (
              <>Cached: {new Date(featureFlags.cacheTimestamp).toLocaleString()}</>
            ) : lastUpdated ? (
              <>Last updated: {lastUpdated.toLocaleTimeString()}</>
            ) : (
              <>Last updated: Unknown</>
            )}
          </span>
        </div>
      </div>
    </div>
  );
}
