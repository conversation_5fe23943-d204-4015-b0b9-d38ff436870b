// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
// Note: Currently using JWT session strategy - Session table is unused but kept for future flexibility

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Session table - CURRENTLY UNUSED (JWT strategy)
// This table would be used if switching to database session strategy
// Kept for future flexibility and NextAuth schema compatibility
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  accounts      Account[]
  sessions      Session[]
  featureFlags  FeatureFlag[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Feature Flag Management System
// Provides database-driven feature flags with user relations and metadata support
model FeatureFlag {
  id          String   @id @default(cuid())
  key         String   @unique // Unique identifier for the feature flag
  name        String   // Human-readable name
  description String?  // Optional description
  enabled     Boolean  @default(false) // Whether the flag is enabled
  group       String   // Feature group (auth_providers, ui_components, api_endpoints)
  metadata    String?  // JSON metadata for additional configuration

  // User relations for audit trail
  createdBy   String?  // User who created this flag
  updatedBy   String?  // User who last updated this flag
  creator     User?    @relation(fields: [createdBy], references: [id], onDelete: SetNull)

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Performance indexes
  @@index([group])
  @@index([enabled])
  @@index([key, enabled])
  @@index([group, enabled])
}
