# SCE Portal Environment Variables Template
# Copy this file to .env.local and replace placeholder values with actual credentials

# NextAuth Configuration
NEXTAUTH_URL="${NEXTAUTH_URL:-http://localhost:3000}"
NEXTAUTH_SECRET="${NEXTAUTH_SECRET:-your-nextauth-secret-here}"

# Database Configuration
DATABASE_URL="${DATABASE_URL:-file:./dev.db}"

# Google OAuth Provider
GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID:-your-google-client-id}"
GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET:-your-google-client-secret}"

# GitHub OAuth Provider
GITHUB_ID="${GITHUB_ID:-your-github-client-id}"
GITHUB_SECRET="${GITHUB_SECRET:-your-github-client-secret}"

# Amazon OAuth Provider
AMAZON_CLIENT_ID="${AMAZON_CLIENT_ID:-your-amazon-client-id}"
AMAZON_CLIENT_SECRET="${AMAZON_CLIENT_SECRET:-your-amazon-client-secret}"

# Email Provider (Resend)
RESEND_API_KEY="${RESEND_API_KEY:-your-resend-api-key}"
RESEND_DOMAIN="${RESEND_DOMAIN:-your-domain.com}"
RESEND_SENDER="${RESEND_SENDER:-<EMAIL>}"
RESEND_RECIPIENT="${RESEND_RECIPIENT:-<EMAIL>}"

# AI Agent Configuration
AI_AGENT_NAME="${AI_AGENT_NAME:-Agent for Kevin Lonigro}"
AI_AGENT_EMAIL="${AI_AGENT_EMAIL:-<EMAIL>}"
AI_AGENT_PAT="${AI_AGENT_PAT:-your-github-personal-access-token}"
GH_TOKEN="${AI_AGENT_PAT}"

# =============================================================================
# FEATURE MANAGEMENT SYSTEM - Environment-Based Configuration
# =============================================================================

# Authentication Provider Feature Flags
# Control which authentication providers are available to users
# Are overridden by database settings if present
# When seeding database, ensure to enable a provider otherwise users will be unable to sign in
FEATURE_GOOGLE_OAUTH_ENABLED="${FEATURE_GOOGLE_OAUTH_ENABLED:-false}"
FEATURE_GITHUB_OAUTH_ENABLED="${FEATURE_GITHUB_OAUTH_ENABLED:-false}"
FEATURE_AMAZON_OAUTH_ENABLED="${FEATURE_AMAZON_OAUTH_ENABLED:-false}"
FEATURE_EMAIL_MAGIC_LINKS_ENABLED="${FEATURE_EMAIL_MAGIC_LINKS_ENABLED:-false}"

# UI Component Feature Flags
# Control visibility of various UI components
FEATURE_FOOTER_ENABLED="${FEATURE_FOOTER_ENABLED:-false}"

# API Endpoint Feature Flags
# Control availability of API endpoints
FEATURE_DEBUG_SESSION_API_ENABLED="${FEATURE_DEBUG_SESSION_API_ENABLED:-false}"
FEATURE_SESSION_CLEANUP_API_ENABLED="${FEATURE_SESSION_CLEANUP_API_ENABLED:-false}"

# =============================================================================
# Feature Flag Usage Examples:
# =============================================================================
# 
# To disable Google OAuth:
# FEATURE_GOOGLE_OAUTH_ENABLED=false
#
# To disable all OAuth providers and only allow email:
# FEATURE_GOOGLE_OAUTH_ENABLED=false
# FEATURE_GITHUB_OAUTH_ENABLED=false
# FEATURE_AMAZON_OAUTH_ENABLED=false
# FEATURE_EMAIL_MAGIC_LINKS_ENABLED=true
#
# To enable debug API in development:
# FEATURE_DEBUG_SESSION_API_ENABLED=true
#
# =============================================================================
# Notes:
# =============================================================================
# 
# 1. Feature flags use boolean values: "true" or "false"
# 2. Default values are specified after the ":-" in the template syntax
# 3. All authentication providers are disabled by default explicitness
# 4. Debug APIs are disabled by default for security
# 5. Changes to feature flags require server restart in development
# 6. In production, consider using your deployment platform's environment
#    variable management (Vercel, Netlify, etc.) instead of .env files
#
# =============================================================================
