/**
 * Hybrid Feature Configuration System
 * 
 * Combines database-driven feature flags with environment variable fallbacks.
 * Database values override environment variables when available.
 * Provides caching and graceful fallback to environment-based system.
 */

import {
  getAllFeatureFlags,
  getFeatureFlagByKey,
  type FeatureFlagWithMetadata
} from './feature-flag-db';

import {
  featureConfig as envFeatureConfig,
  getFeatureStats as envGetFeatureStats,
  type FeatureFlag as EnvFeatureFlag
} from './feature-config';

// Extended types for hybrid system
export interface HybridFeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  group: string;
  metadata?: Record<string, unknown>;
  source: 'database' | 'environment';
  createdAt?: Date;
  updatedAt?: Date;
}

export interface HybridFeatureGroup {
  key: string;
  name: string;
  description: string;
  features: HybridFeatureFlag[];
}

export interface HybridFeatureConfig {
  groups: HybridFeatureGroup[];
  getAllFeatures: () => Promise<HybridFeatureFlag[]>;
  getFeaturesByGroup: (groupKey: string) => Promise<HybridFeatureFlag[]>;
  isFeatureEnabled: (featureKey: string) => Promise<boolean>;
  getFeatureMetadata: (featureKey: string) => Promise<Record<string, unknown> | undefined>;
  refresh: () => Promise<void>;
}

// Cache for hybrid configuration
interface HybridCacheEntry {
  features: HybridFeatureFlag[];
  groups: HybridFeatureGroup[];
  timestamp: number;
}

let hybridCache: HybridCacheEntry | null = null;
const HYBRID_CACHE_TTL = 2 * 60 * 1000; // 2 minutes (shorter than DB cache)

/**
 * Clear the hybrid configuration cache
 */
export function clearHybridCache(): void {
  hybridCache = null;
}

/**
 * Check if hybrid cache is valid
 */
function isHybridCacheValid(): boolean {
  return hybridCache !== null && (Date.now() - hybridCache.timestamp) < HYBRID_CACHE_TTL;
}

/**
 * Transform database feature flag to hybrid format
 */
function transformDbToHybrid(dbFlag: FeatureFlagWithMetadata): HybridFeatureFlag {
  return {
    key: dbFlag.key,
    name: dbFlag.name,
    description: dbFlag.description || '',
    enabled: dbFlag.enabled,
    group: dbFlag.group,
    metadata: dbFlag.metadata,
    source: 'database',
    createdAt: dbFlag.createdAt,
    updatedAt: dbFlag.updatedAt
  };
}

/**
 * Transform environment feature flag to hybrid format
 */
function transformEnvToHybrid(envFlag: EnvFeatureFlag): HybridFeatureFlag {
  return {
    key: envFlag.key,
    name: envFlag.name,
    description: envFlag.description,
    enabled: envFlag.enabled,
    group: envFlag.group,
    metadata: envFlag.metadata,
    source: 'environment'
  };
}

/**
 * Get merged feature flags (database overrides environment)
 */
async function getMergedFeatures(): Promise<HybridFeatureFlag[]> {
  try {
    // Get database flags
    const dbFlags = await getAllFeatureFlags();
    const dbFlagMap = new Map(dbFlags.map(flag => [flag.key, transformDbToHybrid(flag)]));

    // Get environment flags
    const envFlags = envFeatureConfig.getAllFeatures();
    const envFlagMap = new Map(envFlags.map(flag => [flag.key, transformEnvToHybrid(flag)]));

    // Merge: database flags override environment flags
    const mergedFlags: HybridFeatureFlag[] = [];
    const allKeys = new Set([...dbFlagMap.keys(), ...envFlagMap.keys()]);

    for (const key of allKeys) {
      const dbFlag = dbFlagMap.get(key);
      const envFlag = envFlagMap.get(key);

      if (dbFlag) {
        // Database flag exists, use it
        mergedFlags.push(dbFlag);
      } else if (envFlag) {
        // Only environment flag exists, use it
        mergedFlags.push(envFlag);
      }
    }

    return mergedFlags.sort((a, b) => {
      // Sort by group first, then by name
      if (a.group !== b.group) {
        return a.group.localeCompare(b.group);
      }
      return a.name.localeCompare(b.name);
    });
  } catch (error) {
    console.error('Error getting merged features, falling back to environment:', error);
    // Fallback to environment-only features
    return envFeatureConfig.getAllFeatures().map(transformEnvToHybrid);
  }
}

/**
 * Build hybrid feature groups
 */
async function buildHybridGroups(features: HybridFeatureFlag[]): Promise<HybridFeatureGroup[]> {
  const groupMap = new Map<string, HybridFeatureFlag[]>();

  // Group features by group key
  for (const feature of features) {
    if (!groupMap.has(feature.group)) {
      groupMap.set(feature.group, []);
    }
    groupMap.get(feature.group)!.push(feature);
  }

  // Build groups with metadata from environment config
  const groups: HybridFeatureGroup[] = [];
  const envGroups = envFeatureConfig.groups;

  for (const [groupKey, groupFeatures] of groupMap) {
    const envGroup = envGroups.find(g => g.key === groupKey);
    
    groups.push({
      key: groupKey,
      name: envGroup?.name || groupKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: envGroup?.description || `Features in the ${groupKey} group`,
      features: groupFeatures
    });
  }

  return groups.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Get or build hybrid configuration with caching
 */
async function getHybridConfig(): Promise<{ features: HybridFeatureFlag[]; groups: HybridFeatureGroup[] }> {
  // Return cached data if valid
  if (isHybridCacheValid() && hybridCache) {
    return {
      features: hybridCache.features,
      groups: hybridCache.groups
    };
  }

  try {
    const features = await getMergedFeatures();
    const groups = await buildHybridGroups(features);

    // Update cache
    hybridCache = {
      features,
      groups,
      timestamp: Date.now()
    };

    return { features, groups };
  } catch (error) {
    console.error('Error building hybrid config:', error);
    throw error;
  }
}

/**
 * Create hybrid feature configuration instance
 */
function createHybridFeatureConfig(): HybridFeatureConfig {
  return {
    groups: [], // Will be populated by methods

    async getAllFeatures(): Promise<HybridFeatureFlag[]> {
      const { features } = await getHybridConfig();
      return features;
    },

    async getFeaturesByGroup(groupKey: string): Promise<HybridFeatureFlag[]> {
      const { groups } = await getHybridConfig();
      const group = groups.find(g => g.key === groupKey);
      return group ? group.features : [];
    },

    async isFeatureEnabled(featureKey: string): Promise<boolean> {
      try {
        // Try database first
        const dbFlag = await getFeatureFlagByKey(featureKey);
        if (dbFlag) {
          return dbFlag.enabled;
        }

        // Fallback to environment
        return envFeatureConfig.isFeatureEnabled(featureKey);
      } catch (error) {
        console.error(`Error checking feature flag ${featureKey}:`, error);
        // Fallback to environment on error
        return envFeatureConfig.isFeatureEnabled(featureKey);
      }
    },

    async getFeatureMetadata(featureKey: string): Promise<Record<string, unknown> | undefined> {
      try {
        // Try database first
        const dbFlag = await getFeatureFlagByKey(featureKey);
        if (dbFlag) {
          return dbFlag.metadata;
        }

        // Fallback to environment
        return envFeatureConfig.getFeatureMetadata(featureKey);
      } catch (error) {
        console.error(`Error getting metadata for feature flag ${featureKey}:`, error);
        // Fallback to environment on error
        return envFeatureConfig.getFeatureMetadata(featureKey);
      }
    },

    async refresh(): Promise<void> {
      clearHybridCache();
      await getHybridConfig(); // Rebuild cache
    }
  };
}

// Export the hybrid feature configuration instance
export const hybridFeatureConfig = createHybridFeatureConfig();

/**
 * Hybrid authentication provider checking
 */
export async function isAuthProviderEnabled(provider: 'google' | 'github' | 'amazon' | 'email'): Promise<boolean> {
  const providerMap = {
    google: 'google_oauth',
    github: 'github_oauth',
    amazon: 'amazon_oauth',
    email: 'email_magic_links'
  };

  const featureKey = providerMap[provider];
  return await hybridFeatureConfig.isFeatureEnabled(featureKey);
}

/**
 * Get enabled authentication providers (hybrid)
 */
export async function getEnabledAuthProviders(): Promise<string[]> {
  const providers = ['google', 'github', 'amazon', 'email'] as const;
  const enabledProviders: string[] = [];

  for (const provider of providers) {
    if (await isAuthProviderEnabled(provider)) {
      enabledProviders.push(provider);
    }
  }

  return enabledProviders;
}

/**
 * Get hybrid feature flag statistics
 */
export async function getFeatureStats(): Promise<{
  total: number;
  enabled: number;
  disabled: number;
  groups: number;
  enabledPercentage: number;
  databaseFlags: number;
  environmentFlags: number;
}> {
  try {
    const { features, groups } = await getHybridConfig();
    const enabled = features.filter(f => f.enabled).length;
    const databaseFlags = features.filter(f => f.source === 'database').length;
    const environmentFlags = features.filter(f => f.source === 'environment').length;

    return {
      total: features.length,
      enabled,
      disabled: features.length - enabled,
      groups: groups.length,
      enabledPercentage: features.length > 0 ? Math.round((enabled / features.length) * 100) : 0,
      databaseFlags,
      environmentFlags
    };
  } catch (error) {
    console.error('Error getting hybrid feature stats:', error);
    // Fallback to environment stats
    const envStats = envGetFeatureStats();
    return {
      ...envStats,
      databaseFlags: 0,
      environmentFlags: envStats.total
    };
  }
}
