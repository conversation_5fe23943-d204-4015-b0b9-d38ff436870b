import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import {
  hybridFeatureConfig,
  isAuthProviderEnabled,
  getEnabledAuthProviders,
  getFeatureStats
} from '@/app/lib/hybrid-feature-config';
import {
  createFeatureFlag,
  getLastModificationTime,
  getCacheTimestamp
} from '@/app/lib/feature-flag-db';

export async function GET() {
  try {
    // Get all features organized by group (hybrid system)
    const allFeatures = await hybridFeatureConfig.getAllFeatures();
    const enabledProviders = await getEnabledAuthProviders();
    const stats = await getFeatureStats();

    // Get timestamp information for meaningful "last updated" display
    const dataLastModified = await getLastModificationTime();
    const cacheTimestamp = getCacheTimestamp();

    const featureFlags = {
      // Authentication provider flags (backward compatibility)
      authProviders: {
        google: await isAuthProviderEnabled('google'),
        github: await isAuthProviderEnabled('github'),
        amazon: await isAuthProviderEnabled('amazon'),
        email: await isAuthProviderEnabled('email'),
      },

      // UI Component flags
      uiComponents: {
        footer: await hybridFeatureConfig.isFeatureEnabled('footer'),
      },

      // API Endpoint flags
      apiEndpoints: {
        debugSession: await hybridFeatureConfig.isFeatureEnabled('debug_session_api'),
        sessionCleanup: await hybridFeatureConfig.isFeatureEnabled('session_cleanup_api'),
      },

      // All features (for advanced usage)
      allFeatures: allFeatures.reduce((acc, feature) => {
        acc[feature.key] = feature.enabled;
        return acc;
      }, {} as Record<string, boolean>),

      // Groups information with hybrid features (including source info)
      groups: await Promise.all(['auth_providers', 'ui_components', 'api_endpoints'].map(async (groupKey) => {
        const groupFeatures = await hybridFeatureConfig.getFeaturesByGroup(groupKey);
        const groupName = groupKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        return {
          key: groupKey,
          name: groupName,
          description: `Features in the ${groupName} group`,
          features: groupFeatures.reduce((acc, feature) => {
            acc[feature.key] = feature.enabled;
            return acc;
          }, {} as Record<string, boolean>),
          // Add source mapping for each feature
          featureSources: groupFeatures.reduce((acc, feature) => {
            acc[feature.key] = feature.source;
            return acc;
          }, {} as Record<string, 'database' | 'environment'>)
        };
      })),

      // Utility functions results
      enabledProviders,
      hasAnyProviderEnabled: enabledProviders.length > 0,
      enabledProviderCount: enabledProviders.length,

      // Statistics (enhanced with hybrid info)
      stats,

      // Metadata
      timestamp: new Date().toISOString(), // API generation time
      dataLastModified: dataLastModified?.toISOString(), // When data was actually last modified
      cacheTimestamp: cacheTimestamp?.toISOString(), // When cache was last populated
      environment: process.env.NODE_ENV,
      source: 'hybrid', // Indicate this is from the hybrid system
    };

    return NextResponse.json(featureFlags, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error fetching feature flags:', error);
    
    // Return safe defaults in case of error
    return NextResponse.json({
      authProviders: {
        google: false,
        github: false,
        amazon: false,
        email: false,
      },
      uiComponents: {
        footer: false,
      },
      apiEndpoints: {
        debugSession: false,
        sessionCleanup: false,
      },
      allFeatures: {},
      groups: [],
      enabledProviders: [],
      hasAnyProviderEnabled: false,
      enabledProviderCount: 0,
      stats: { total: 0, enabled: 0, disabled: 0, groups: 0, enabledPercentage: 0 },
      timestamp: new Date().toISOString(),
      dataLastModified: undefined,
      cacheTimestamp: undefined,
      environment: process.env.NODE_ENV,
      error: 'Failed to load feature flags'
    }, { status: 200 }); // Return 200 to avoid breaking the UI
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(getHybridAuthOptionsForSession());
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { key, name, description, enabled, group, metadata } = body;

    // Validate required fields
    if (!key || !name || !group) {
      return NextResponse.json(
        { error: 'Missing required fields: key, name, group' },
        { status: 400 }
      );
    }

    // Create the feature flag
    const featureFlag = await createFeatureFlag({
      key,
      name,
      description,
      enabled: enabled ?? false,
      group,
      metadata,
      createdBy: session.user.email
    });

    // Clear hybrid cache to reflect changes
    await hybridFeatureConfig.refresh();

    return NextResponse.json(featureFlag, { status: 201 });
  } catch (error) {
    console.error('Error creating feature flag:', error);

    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Feature flag with this key already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create feature flag' },
      { status: 500 }
    );
  }
}
