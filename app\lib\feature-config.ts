/**
 * Feature Management System - Environment-Based Configuration
 * 
 * This module provides a comprehensive feature flag system using environment variables.
 * Features are organized into groups with individual toggles for granular control.
 */

// Feature flag interfaces
export interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  group: string;
  metadata?: Record<string, unknown>;
}

export interface FeatureGroup {
  key: string;
  name: string;
  description: string;
  features: FeatureFlag[];
}

export interface FeatureConfig {
  groups: FeatureGroup[];
  getAllFeatures: () => FeatureFlag[];
  getFeaturesByGroup: (groupKey: string) => FeatureFlag[];
  isFeatureEnabled: (featureKey: string) => boolean;
  getFeatureMetadata: (featureKey: string) => Record<string, unknown> | undefined;
}

// Environment variable parsing utilities
function parseEnvBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
}

// Removed unused function - keeping parseEnvBoolean which is used

// Feature flag definitions with environment variable mapping
const FEATURE_DEFINITIONS = {
  // Authentication Providers Group
  AUTH_PROVIDERS: {
    key: 'auth_providers',
    name: 'Authentication Providers',
    description: 'Control which authentication providers are available to users',
    features: {
      GOOGLE_OAUTH: {
        key: 'google_oauth',
        name: 'Google OAuth',
        description: 'Google OAuth authentication provider',
        envKey: 'FEATURE_GOOGLE_OAUTH_ENABLED',
        defaultEnabled: false,
        metadata: {
          provider: 'google',
          icon: 'GoogleIcon',
          component: 'GoogleSignIn'
        }
      },
      GITHUB_OAUTH: {
        key: 'github_oauth',
        name: 'GitHub OAuth',
        description: 'GitHub OAuth authentication provider',
        envKey: 'FEATURE_GITHUB_OAUTH_ENABLED',
        defaultEnabled: false,
        metadata: {
          provider: 'github',
          icon: 'GitHubIcon',
          component: 'GitHubSignIn'
        }
      },
      AMAZON_OAUTH: {
        key: 'amazon_oauth',
        name: 'Amazon OAuth',
        description: 'Amazon OAuth authentication provider',
        envKey: 'FEATURE_AMAZON_OAUTH_ENABLED',
        defaultEnabled: false,
        metadata: {
          provider: 'amazon',
          icon: 'AmazonIcon',
          component: 'AmazonSignIn'
        }
      },
      EMAIL_MAGIC_LINKS: {
        key: 'email_magic_links',
        name: 'Email Magic Links',
        description: 'Passwordless email authentication',
        envKey: 'FEATURE_EMAIL_MAGIC_LINKS_ENABLED',
        defaultEnabled: false,
        metadata: {
          provider: 'email',
          icon: 'EmailIcon',
          component: 'EmailButton'
        }
      }
    }
  },
  
  // UI Components Group
  UI_COMPONENTS: {
    key: 'ui_components',
    name: 'UI Components',
    description: 'Control visibility of various UI components',
    features: {
      FOOTER: {
        key: 'footer',
        name: 'Footer Component',
        description: 'Site footer with links and information',
        envKey: 'FEATURE_FOOTER_ENABLED',
        defaultEnabled: false,
        metadata: {
          component: 'Footer',
          location: 'global'
        }
      }
    }
  },

  // API Endpoints Group
  API_ENDPOINTS: {
    key: 'api_endpoints',
    name: 'API Endpoints',
    description: 'Control availability of API endpoints',
    features: {
      DEBUG_SESSION: {
        key: 'debug_session',
        name: 'Debug Session API',
        description: 'API endpoint for session debugging',
        envKey: 'FEATURE_DEBUG_SESSION_API_ENABLED',
        defaultEnabled: false, // Disabled by default for security
        metadata: {
          endpoint: '/api/debug-session',
          method: 'GET',
          requiresAuth: true
        }
      },
      SESSION_CLEANUP: {
        key: 'session_cleanup',
        name: 'Session Cleanup API',
        description: 'API endpoint for manual session cleanup',
        envKey: 'FEATURE_SESSION_CLEANUP_API_ENABLED',
        defaultEnabled: false,
        metadata: {
          endpoint: '/api/cleanup-sessions',
          method: 'POST',
          requiresAuth: true
        }
      }
    }
  }
};

// Build feature configuration from definitions
function buildFeatureConfig(): FeatureConfig {
  const groups: FeatureGroup[] = [];

  Object.values(FEATURE_DEFINITIONS).forEach(groupDef => {
    const features: FeatureFlag[] = [];

    Object.values(groupDef.features).forEach(featureDef => {
      const envValue = process.env[featureDef.envKey];
      const enabled = parseEnvBoolean(envValue, featureDef.defaultEnabled);



      features.push({
        key: featureDef.key,
        name: featureDef.name,
        description: featureDef.description,
        enabled,
        group: groupDef.key,
        metadata: featureDef.metadata
      });
    });

    groups.push({
      key: groupDef.key,
      name: groupDef.name,
      description: groupDef.description,
      features
    });
  });

  return {
    groups,
    getAllFeatures: () => groups.flatMap(group => group.features),
    getFeaturesByGroup: (groupKey: string) => {
      const group = groups.find(g => g.key === groupKey);
      return group ? group.features : [];
    },
    isFeatureEnabled: (featureKey: string) => {
      const allFeatures = groups.flatMap(group => group.features);
      const feature = allFeatures.find(f => f.key === featureKey);
      return feature ? feature.enabled : false;
    },
    getFeatureMetadata: (featureKey: string) => {
      const allFeatures = groups.flatMap(group => group.features);
      const feature = allFeatures.find(f => f.key === featureKey);
      return feature?.metadata;
    }
  };
}

// Export the feature configuration instance
export const featureConfig = buildFeatureConfig();

// Convenience functions for common feature checks
export const isAuthProviderEnabled = (provider: 'google' | 'github' | 'amazon' | 'email'): boolean => {
  const providerMap = {
    google: 'google_oauth',
    github: 'github_oauth',
    amazon: 'amazon_oauth',
    email: 'email_magic_links'
  };

  const featureKey = providerMap[provider];
  return featureConfig.isFeatureEnabled(featureKey);
};

export const getEnabledAuthProviders = (): string[] => {
  const authFeatures = featureConfig.getFeaturesByGroup('auth_providers');
  return authFeatures
    .filter(feature => feature.enabled)
    .map(feature => feature.metadata?.provider as string)
    .filter((provider): provider is string => typeof provider === 'string');
};

// Development utilities
export const getFeatureStats = () => {
  const allFeatures = featureConfig.getAllFeatures();
  const enabledCount = allFeatures.filter(f => f.enabled).length;
  const totalCount = allFeatures.length;
  
  return {
    total: totalCount,
    enabled: enabledCount,
    disabled: totalCount - enabledCount,
    groups: featureConfig.groups.length,
    enabledPercentage: Math.round((enabledCount / totalCount) * 100)
  };
};

// Types are already exported above as interfaces
