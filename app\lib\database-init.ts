import { existsSync } from 'fs';
import { prisma } from './prisma';

/**
 * Initialize database if it doesn't exist
 * This is particularly important for SQLite in serverless environments like Vercel
 */
export async function initializeDatabase() {
  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // Only handle SQLite databases
  if (!databaseUrl.startsWith('file:')) {
    console.log('Non-SQLite database detected, skipping initialization');
    return;
  }

  // Extract file path from database URL
  const dbPath = databaseUrl.replace('file:', '');

  // Check if database file exists
  if (!existsSync(dbPath)) {
    console.log(`Database file ${dbPath} does not exist, creating...`);

    try {
      // Use Prisma's $executeRaw to create tables
      // This is more reliable in serverless environments than execSync
      await prisma.$executeRaw`PRAGMA foreign_keys = ON;`;

      // Create tables using Prisma's internal schema
      await createTables();

      console.log(`Database ${dbPath} created and initialized successfully`);
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  } else {
    console.log(`Database ${dbPath} already exists`);
  }
}

/**
 * Create database tables manually
 * This replicates what `prisma db push` does but works in serverless environments
 */
async function createTables() {
  try {
    // Create Account table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "Account" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "userId" TEXT NOT NULL,
        "type" TEXT NOT NULL,
        "provider" TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        "refresh_token" TEXT,
        "access_token" TEXT,
        "expires_at" INTEGER,
        "token_type" TEXT,
        "scope" TEXT,
        "id_token" TEXT,
        "session_state" TEXT,
        FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE
      );
    `;

    // Create Session table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "Session" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "sessionToken" TEXT NOT NULL UNIQUE,
        "userId" TEXT NOT NULL,
        "expires" DATETIME NOT NULL,
        FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE
      );
    `;

    // Create User table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "User" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT,
        "email" TEXT UNIQUE,
        "emailVerified" DATETIME
      );
    `;

    // Create VerificationToken table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "VerificationToken" (
        "identifier" TEXT NOT NULL,
        "token" TEXT NOT NULL UNIQUE,
        "expires" DATETIME NOT NULL
      );
    `;

    // Create unique indexes
    await prisma.$executeRaw`CREATE UNIQUE INDEX IF NOT EXISTS "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");`;
    await prisma.$executeRaw`CREATE UNIQUE INDEX IF NOT EXISTS "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");`;

    console.log('Database tables created successfully');
  } catch (error) {
    console.error('Failed to create tables:', error);
    throw error;
  }
}

/**
 * Ensure database is ready for use
 * Call this before any database operations in serverless environments
 */
export async function ensureDatabaseReady() {
  try {
    await initializeDatabase();
  } catch (error) {
    console.error('Database initialization failed:', error);
    // In production, you might want to handle this more gracefully
    throw error;
  }
}
