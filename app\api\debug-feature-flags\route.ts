import { NextResponse } from 'next/server';
import { featureConfig, isAuthProviderEnabled, getEnabledAuthProviders } from '@/app/lib/feature-config';

export async function GET() {
  // First, let's just check the raw environment variables
  const allEnvVars: Record<string, string | undefined> = {};
  Object.keys(process.env).forEach(key => {
    allEnvVars[key] = process.env[key];
  });

  // Get all environment variables that start with FEATURE_
  const featureEnvVars: Record<string, string | undefined> = {};
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('FEATURE_')) {
      featureEnvVars[key] = process.env[key];
    }
  });

  // Test the parseEnvBoolean function directly
  function parseEnvBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
    if (!value) return defaultValue;
    return value.toLowerCase() === 'true' || value === '1';
  }

  const parseTests = {
    FEATURE_GOOGLE_OAUTH_ENABLED: {
      raw: process.env.FEATURE_GOOGLE_OAUTH_ENABLED,
      parsed: parseEnvBoolean(process.env.FEATURE_GOOGLE_OAUTH_ENABLED, false)
    },
    FEATURE_GITHUB_OAUTH_ENABLED: {
      raw: process.env.FEATURE_GITHUB_OAUTH_ENABLED,
      parsed: parseEnvBoolean(process.env.FEATURE_GITHUB_OAUTH_ENABLED, false)
    },
    FEATURE_AMAZON_OAUTH_ENABLED: {
      raw: process.env.FEATURE_AMAZON_OAUTH_ENABLED,
      parsed: parseEnvBoolean(process.env.FEATURE_AMAZON_OAUTH_ENABLED, false)
    },
    FEATURE_EMAIL_MAGIC_LINKS_ENABLED: {
      raw: process.env.FEATURE_EMAIL_MAGIC_LINKS_ENABLED,
      parsed: parseEnvBoolean(process.env.FEATURE_EMAIL_MAGIC_LINKS_ENABLED, false)
    }
  };

  // Test the actual feature flag system
  const allFeatures = featureConfig.getAllFeatures();
  const authFeatures = featureConfig.getFeaturesByGroup('auth_providers');

  // Test individual provider checks
  const providerTests = {
    google: isAuthProviderEnabled('google'),
    github: isAuthProviderEnabled('github'),
    amazon: isAuthProviderEnabled('amazon'),
    email: isAuthProviderEnabled('email')
  };

  const debugInfo = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    totalEnvVars: Object.keys(allEnvVars).length,
    featureEnvVars,
    parseTests,
    allFeatures: allFeatures.map(f => ({
      key: f.key,
      name: f.name,
      enabled: f.enabled,
      group: f.group
    })),
    authFeatures: authFeatures.map(f => ({
      key: f.key,
      name: f.name,
      enabled: f.enabled,
      metadata: f.metadata
    })),
    providerTests,
    enabledAuthProviders: getEnabledAuthProviders(),
    hasAnyProviderEnabled: getEnabledAuthProviders().length > 0,
    nodeEnv: process.env.NODE_ENV,
    nextAuthUrl: process.env.NEXTAUTH_URL
  };

  return NextResponse.json(debugInfo, { status: 200 });
}
