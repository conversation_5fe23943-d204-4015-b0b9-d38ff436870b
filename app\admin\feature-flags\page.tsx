/**
 * Feature Flags Admin Page
 * 
 * Provides a comprehensive admin interface for managing feature flags
 * with authentication protection and real-time updates.
 */

import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import FeatureFlagAdmin from '@/app/components/FeatureFlagAdmin';

export default async function FeatureFlagsAdminPage() {
  // Check authentication
  const session = await getServerSession(getHybridAuthOptionsForSession());
  
  if (!session?.user?.email) {
    redirect('/signin?callbackUrl=/admin/feature-flags');
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Feature Flags Administration
          </h1>
          <p className="mt-2 text-gray-600">
            Manage feature flags with real-time updates and hybrid configuration support.
          </p>
          <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
            <span>Logged in as: {session.user.email}</span>
            <span>•</span>
            <span>Admin Access</span>
          </div>
        </div>

        {/* Admin Interface */}
        <FeatureFlagAdmin />
      </div>
    </div>
  );
}

export const metadata = {
  title: 'Feature Flags Admin - SCE Portal',
  description: 'Administrative interface for managing feature flags',
};
