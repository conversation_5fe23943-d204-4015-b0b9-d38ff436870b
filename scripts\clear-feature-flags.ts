#!/usr/bin/env tsx

/**
 * Clear Feature Flags Script
 * 
 * This script removes all feature flags from the database to allow for
 * clean testing of the admin interface and seeding functionality.
 * 
 * Usage:
 *   pnpm db:clear-flags
 * 
 * What it does:
 * 1. Connects to the database
 * 2. Counts existing feature flags
 * 3. Deletes all feature flags
 * 4. Clears the hybrid feature flag cache
 * 5. Reports the results
 */

import { PrismaClient } from '@prisma/client';
import { clearHybridCache } from '../app/lib/hybrid-feature-config.js';
import { clearFeatureFlagCache } from '../app/lib/feature-flag-db.js';

const prisma = new PrismaClient();

async function clearFeatureFlags() {
  try {
    console.log('🗑️  Clearing Feature Flags Database...\n');

    // Count existing flags before deletion
    const existingCount = await prisma.featureFlag.count();
    console.log(`📊 Found ${existingCount} feature flags in database`);

    if (existingCount === 0) {
      console.log('✅ Database is already clean - no feature flags to remove');
      return;
    }

    // Get list of flags that will be deleted (for logging)
    const flagsToDelete = await prisma.featureFlag.findMany({
      select: {
        key: true,
        name: true,
        group: true,
        enabled: true
      },
      orderBy: { group: 'asc' }
    });

    console.log('\n🔍 Feature flags to be deleted:');
    flagsToDelete.forEach(flag => {
      const status = flag.enabled ? '✅' : '❌';
      console.log(`   ${status} ${flag.key} (${flag.group}) - ${flag.name}`);
    });

    // Confirm deletion
    console.log('\n⚠️  This will permanently delete all feature flags from the database.');
    console.log('   The hybrid system will fall back to environment variables.');
    
    // Delete all feature flags
    console.log('\n🗑️  Deleting all feature flags...');
    const deleteResult = await prisma.featureFlag.deleteMany({});
    
    console.log(`✅ Successfully deleted ${deleteResult.count} feature flags`);

    // Clear all caches
    console.log('🧹 Clearing feature flag caches...');
    clearFeatureFlagCache();
    clearHybridCache();
    console.log('✅ Caches cleared successfully');

    // Verify deletion
    const remainingCount = await prisma.featureFlag.count();
    if (remainingCount === 0) {
      console.log('✅ Database cleanup verified - no feature flags remain');
    } else {
      console.log(`⚠️  Warning: ${remainingCount} feature flags still exist`);
    }

    console.log('\n📝 Next steps:');
    console.log('   1. Visit /admin/feature-flags to test the empty state');
    console.log('   2. Use "Seed Defaults" to repopulate with default flags');
    console.log('   3. Check Prisma Studio: pnpm db:studio');
    console.log('   4. Verify hybrid system falls back to environment variables');

  } catch (error) {
    console.error('❌ Error clearing feature flags:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
clearFeatureFlags()
  .then(() => {
    console.log('\n🎉 Feature flags cleared successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
