import { getServerSession } from 'next-auth';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import { prisma } from '@/app/lib/prisma';
import { NextResponse } from 'next/server';
import { getSessionStats } from '@/app/lib/session-cleanup';

export async function GET() {
  try {
    // Get server session
    const session = await getServerSession(getHybridAuthOptionsForSession());

    // In production, only show basic session info
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({
        serverSession: session ? {
          user: { email: session.user?.email },
          authenticated: true
        } : null,
        environment: 'production',
        timestamp: new Date().toISOString(),
        note: "Limited debug info in production"
      });
    }

    // Full debug info in development
    const users = await prisma.user.findMany({
      include: {
        sessions: true,
        accounts: true
      }
    });

    const sessions = await prisma.session.findMany({
      include: {
        user: true
      }
    });

    const sessionStats = await getSessionStats();

    return NextResponse.json({
      serverSession: session,
      databaseUsers: users,
      databaseSessions: sessions,
      sessionStats,
      environment: 'development',
      timestamp: new Date().toISOString(),
      note: "JWT session strategy - database sessions are obsolete and should be cleared"
    });
  } catch (error) {
    console.error('Debug session error:', error);
    return NextResponse.json({
      error: 'Failed to debug session',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Add a POST endpoint to clear old database sessions
export async function POST() {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Debug session API not available in production' },
      { status: 403 }
    );
  }

  try {
    // Clear all database sessions since we're using JWT now
    const deletedSessions = await prisma.session.deleteMany({});

    return NextResponse.json({
      message: 'Database sessions cleared - users will need to re-authenticate',
      deletedCount: deletedSessions.count,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Clear sessions error:', error);
    return NextResponse.json(
      { error: 'Failed to clear sessions' },
      { status: 500 }
    );
  }
}
