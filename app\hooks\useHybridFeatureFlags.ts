/**
 * React Hook for Hybrid Feature Flag Management
 * 
 * Provides client-side access to hybrid feature flags (database + environment)
 * with auto-refresh capabilities and performance optimization.
 */

'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

// Types for the hybrid API response
interface HybridFeatureFlagsResponse {
  authProviders: {
    google: boolean;
    github: boolean;
    amazon: boolean;
    email: boolean;
  };
  uiComponents: {
    footer: boolean;
  };
  apiEndpoints: {
    debugSession: boolean;
    sessionCleanup: boolean;
  };
  allFeatures: Record<string, boolean>;
  groups: Array<{
    key: string;
    name: string;
    description: string;
    features: Record<string, boolean>;
    featureSources?: Record<string, 'database' | 'environment'>;
  }>;
  enabledProviders: string[];
  hasAnyProviderEnabled: boolean;
  enabledProviderCount: number;
  stats: {
    total: number;
    enabled: number;
    disabled: number;
    groups: number;
    enabledPercentage: number;
    databaseFlags?: number;
    environmentFlags?: number;
  };
  timestamp: string; // API generation time
  dataLastModified?: string; // When feature flag data was actually last modified
  cacheTimestamp?: string; // When cache was last populated
  environment: string;
  source: string;
  error?: string;
}

interface UseHybridFeatureFlagsReturn {
  // Feature flag data
  featureFlags: HybridFeatureFlagsResponse | null;
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
  
  // Convenience methods
  isFeatureEnabled: (featureKey: string) => boolean;
  isAuthProviderEnabled: (provider: 'google' | 'github' | 'amazon' | 'email') => boolean;
  getEnabledAuthProviders: () => string[];
  
  // Utility methods
  refresh: () => Promise<void>;
  getStats: () => HybridFeatureFlagsResponse['stats'] | null;
  getGroups: () => HybridFeatureFlagsResponse['groups'];
  
  // Metadata
  lastUpdated: Date | null;
  source: string;
}

export function useHybridFeatureFlags(autoRefresh = false, refreshInterval = 30000): UseHybridFeatureFlagsReturn {
  const [featureFlags, setFeatureFlags] = useState<HybridFeatureFlagsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchFeatureFlags = useCallback(async () => {
    try {
      setError(null);
      
      const response = await fetch('/api/feature-flags', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: HybridFeatureFlagsResponse = await response.json();
      
      setFeatureFlags(data);
      setLastUpdated(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch hybrid feature flags';
      setError(errorMessage);
      console.error('Error fetching hybrid feature flags:', err);
      
      // Set safe defaults
      setFeatureFlags({
        authProviders: {
          google: false,
          github: false,
          amazon: false,
          email: false,
        },
        uiComponents: {
          footer: false,
        },
        apiEndpoints: {
          debugSession: false,
          sessionCleanup: false,
        },
        allFeatures: {},
        groups: [],
        enabledProviders: [],
        hasAnyProviderEnabled: false,
        enabledProviderCount: 0,
        stats: { total: 0, enabled: 0, disabled: 0, groups: 0, enabledPercentage: 0 },
        timestamp: new Date().toISOString(),
        dataLastModified: undefined,
        cacheTimestamp: undefined,
        environment: 'unknown',
        source: 'fallback',
        error: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchFeatureFlags();
  }, [fetchFeatureFlags]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchFeatureFlags, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchFeatureFlags]);

  // Memoized convenience methods
  const utilities = useMemo(() => ({
    isFeatureEnabled: (featureKey: string): boolean => {
      return featureFlags?.allFeatures[featureKey] ?? false;
    },
    
    isAuthProviderEnabled: (provider: 'google' | 'github' | 'amazon' | 'email'): boolean => {
      return featureFlags?.authProviders[provider] ?? false;
    },
    
    getEnabledAuthProviders: (): string[] => {
      return featureFlags?.enabledProviders ?? [];
    },
    
    refresh: async (): Promise<void> => {
      setIsLoading(true);
      await fetchFeatureFlags();
    },
    
    getStats: (): HybridFeatureFlagsResponse['stats'] | null => {
      return featureFlags?.stats ?? null;
    },
    
    getGroups: (): HybridFeatureFlagsResponse['groups'] => {
      return featureFlags?.groups ?? [];
    }
  }), [featureFlags, fetchFeatureFlags]);

  return {
    featureFlags,
    isLoading,
    error,
    lastUpdated,
    source: featureFlags?.source ?? 'unknown',
    ...utilities
  };
}

/**
 * Hook for authentication provider flags specifically
 */
export function useAuthProviderFlags() {
  const { featureFlags, isLoading, error, isAuthProviderEnabled } = useHybridFeatureFlags();

  return useMemo(() => ({
    isGoogleEnabled: isAuthProviderEnabled('google'),
    isGitHubEnabled: isAuthProviderEnabled('github'),
    isAmazonEnabled: isAuthProviderEnabled('amazon'),
    isEmailEnabled: isAuthProviderEnabled('email'),
    enabledProviders: featureFlags?.enabledProviders ?? [],
    hasAnyProviderEnabled: featureFlags?.hasAnyProviderEnabled ?? false,
    enabledProviderCount: featureFlags?.enabledProviderCount ?? 0,
    isLoading,
    error
  }), [featureFlags, isAuthProviderEnabled, isLoading, error]);
}

/**
 * Hook for UI component flags specifically
 */
export function useUIComponentFlags() {
  const { featureFlags, isLoading, error, isFeatureEnabled } = useHybridFeatureFlags();

  return useMemo(() => ({
    isFooterEnabled: isFeatureEnabled('footer'),
    uiComponents: featureFlags?.uiComponents ?? { footer: false },
    isLoading,
    error
  }), [featureFlags, isFeatureEnabled, isLoading, error]);
}

/**
 * Hook for API endpoint flags specifically
 */
export function useAPIEndpointFlags() {
  const { featureFlags, isLoading, error, isFeatureEnabled } = useHybridFeatureFlags();

  return useMemo(() => ({
    isDebugSessionEnabled: isFeatureEnabled('debug_session_api'),
    isSessionCleanupEnabled: isFeatureEnabled('session_cleanup_api'),
    apiEndpoints: featureFlags?.apiEndpoints ?? { debugSession: false, sessionCleanup: false },
    isLoading,
    error
  }), [featureFlags, isFeatureEnabled, isLoading, error]);
}
