'use client';

import { useState, useEffect } from 'react';

// Types for the API response
interface FeatureFlagsResponse {
  authProviders: {
    google: boolean;
    github: boolean;
    amazon: boolean;
    email: boolean;
  };
  uiComponents: {
    footer: boolean;
  };
  apiEndpoints: {
    debugSession: boolean;
    sessionCleanup: boolean;
  };
  allFeatures: Record<string, boolean>;
  groups: Array<{
    key: string;
    name: string;
    description: string;
    features: Record<string, boolean>;
  }>;
  enabledProviders: string[];
  hasAnyProviderEnabled: boolean;
  enabledProviderCount: number;
  stats: {
    total: number;
    enabled: number;
    disabled: number;
    groups: number;
    enabledPercentage: number;
  };
  timestamp: string;
  environment: string;
  error?: string;
}

// Hook return type for auth providers
interface UseAuthProviderFlagsReturn {
  // Individual provider checks
  isGoogleEnabled: boolean;
  isGitHubEnabled: boolean;
  isAmazonEnabled: boolean;
  isEmailEnabled: boolean;
  
  // Utility functions
  hasAnyProviderEnabled: () => boolean;
  getEnabledProviderCount: () => number;
  getEnabledProviders: () => string[];
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
  
  // Refresh function
  refresh: () => void;
}

/**
 * Client-side hook for authentication provider feature flags
 * Fetches feature flag states from the server API endpoint
 */
export function useAuthProviderFlags(): UseAuthProviderFlagsReturn {
  const [featureFlags, setFeatureFlags] = useState<FeatureFlagsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeatureFlags = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/feature-flags', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data: FeatureFlagsResponse = await response.json();
      
      if (data.error) {
        setError(data.error);
      }
      
      setFeatureFlags(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch feature flags';
      setError(errorMessage);
      console.error('Error fetching feature flags:', err);
      
      // Set safe defaults
      setFeatureFlags({
        authProviders: {
          google: false,
          github: false,
          amazon: false,
          email: false,
        },
        uiComponents: {
          footer: false,
        },
        apiEndpoints: {
          debugSession: false,
          sessionCleanup: false,
        },
        allFeatures: {},
        groups: [],
        enabledProviders: [],
        hasAnyProviderEnabled: false,
        enabledProviderCount: 0,
        stats: { total: 0, enabled: 0, disabled: 0, groups: 0, enabledPercentage: 0 },
        timestamp: new Date().toISOString(),
        environment: 'unknown',
        error: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFeatureFlags();
  }, []);

  return {
    // Individual provider checks
    isGoogleEnabled: featureFlags?.authProviders.google ?? false,
    isGitHubEnabled: featureFlags?.authProviders.github ?? false,
    isAmazonEnabled: featureFlags?.authProviders.amazon ?? false,
    isEmailEnabled: featureFlags?.authProviders.email ?? false,
    
    // Utility functions
    hasAnyProviderEnabled: () => featureFlags?.hasAnyProviderEnabled ?? false,
    getEnabledProviderCount: () => featureFlags?.enabledProviderCount ?? 0,
    getEnabledProviders: () => featureFlags?.enabledProviders ?? [],
    
    // Loading and error states
    isLoading,
    error,
    
    // Refresh function
    refresh: fetchFeatureFlags,
  };
}

/**
 * Simple hook for checking if any auth providers are enabled
 * Useful for conditional rendering without full feature flag data
 */
export function useHasAuthProviders(): { hasProviders: boolean; isLoading: boolean } {
  const { hasAnyProviderEnabled, isLoading } = useAuthProviderFlags();

  return {
    hasProviders: hasAnyProviderEnabled(),
    isLoading
  };
}

/**
 * Hook for UI component feature flags
 */
export function useUIComponentFlags() {
  const [featureFlags, setFeatureFlags] = useState<FeatureFlagsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeatureFlags = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/feature-flags', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: FeatureFlagsResponse = await response.json();

      if (data.error) {
        setError(data.error);
      }

      setFeatureFlags(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch feature flags';
      setError(errorMessage);
      console.error('Error fetching UI component feature flags:', err);

      // Set safe defaults
      setFeatureFlags({
        authProviders: { google: false, github: false, amazon: false, email: false },
        uiComponents: { footer: false },
        apiEndpoints: { debugSession: false, sessionCleanup: false },
        allFeatures: {},
        groups: [],
        enabledProviders: [],
        hasAnyProviderEnabled: false,
        enabledProviderCount: 0,
        stats: { total: 0, enabled: 0, disabled: 0, groups: 0, enabledPercentage: 0 },
        timestamp: new Date().toISOString(),
        environment: 'unknown',
        error: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFeatureFlags();
  }, []);

  return {
    // UI Component flags
    isFooterEnabled: featureFlags?.uiComponents.footer ?? false,

    // Loading and error states
    isLoading,
    error,

    // Refresh function
    refresh: fetchFeatureFlags,
  };
}

/**
 * General purpose feature flag hook
 * Provides access to any feature flag by key
 */
export function useFeatureFlag(featureKey: string): {
  isEnabled: boolean;
  isLoading: boolean;
  error: string | null;
} {
  const [featureFlags, setFeatureFlags] = useState<FeatureFlagsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeatureFlags = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/feature-flags', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: FeatureFlagsResponse = await response.json();

      if (data.error) {
        setError(data.error);
      }

      setFeatureFlags(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch feature flags';
      setError(errorMessage);
      console.error('Error fetching feature flag:', err);

      // Set safe defaults
      setFeatureFlags({
        authProviders: { google: false, github: false, amazon: false, email: false },
        uiComponents: { footer: false },
        apiEndpoints: { debugSession: false, sessionCleanup: false },
        allFeatures: {},
        groups: [],
        enabledProviders: [],
        hasAnyProviderEnabled: false,
        enabledProviderCount: 0,
        stats: { total: 0, enabled: 0, disabled: 0, groups: 0, enabledPercentage: 0 },
        timestamp: new Date().toISOString(),
        environment: 'unknown',
        error: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFeatureFlags();
  }, []);

  return {
    isEnabled: featureFlags?.allFeatures[featureKey] ?? false,
    isLoading,
    error,
  };
}
