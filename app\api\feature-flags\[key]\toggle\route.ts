import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import { toggleFeatureFlag, getFeatureFlagByKey } from '@/app/lib/feature-flag-db';
import { hybridFeatureConfig } from '@/app/lib/hybrid-feature-config';

interface RouteParams {
  params: Promise<{
    key: string;
  }>;
}

export async function PATCH(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(getHybridAuthOptionsForSession());
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { key } = await params;

    if (!key) {
      return NextResponse.json(
        { error: 'Feature flag key is required' },
        { status: 400 }
      );
    }

    // Check if feature flag exists in database
    const existingFlag = await getFeatureFlagByKey(key);
    if (!existingFlag) {
      return NextResponse.json(
        { error: 'Feature flag not found in database. Only database flags can be toggled.' },
        { status: 404 }
      );
    }

    // Toggle the feature flag
    const toggledFlag = await toggleFeatureFlag(key, session.user.email);

    // Clear hybrid cache to reflect changes
    await hybridFeatureConfig.refresh();

    return NextResponse.json({
      ...toggledFlag,
      message: `Feature flag '${key}' ${toggledFlag.enabled ? 'enabled' : 'disabled'} successfully`
    });
  } catch (error) {
    const { key } = await params;
    console.error(`Error toggling feature flag ${key}:`, error);
    return NextResponse.json(
      { error: 'Failed to toggle feature flag' },
      { status: 500 }
    );
  }
}
