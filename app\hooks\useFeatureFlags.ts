/**
 * React Hook for Feature Flag Management
 * 
 * Provides client-side access to feature flags with type safety and performance optimization.
 * Integrates with the environment-based feature configuration system.
 */

'use client';

import React, { useMemo } from 'react';
import { 
  featureConfig, 
  isAuthProviderEnabled, 
  getEnabledAuthProviders,
  getFeatureStats,
  type FeatureFlag, 
  type FeatureGroup 
} from '@/app/lib/feature-config';

// Hook return type
interface UseFeatureFlagsReturn {
  // Core feature checking
  isFeatureEnabled: (featureKey: string) => boolean;
  getFeatureMetadata: (featureKey: string) => Record<string, unknown> | undefined;
  
  // Group operations
  getFeaturesByGroup: (groupKey: string) => FeatureFlag[];
  getAllFeatures: () => FeatureFlag[];
  getGroups: () => FeatureGroup[];
  
  // Authentication provider utilities
  isAuthProviderEnabled: (provider: 'google' | 'github' | 'amazon' | 'email') => boolean;
  getEnabledAuthProviders: () => string[];
  
  // Statistics and debugging
  getStats: () => {
    total: number;
    enabled: number;
    disabled: number;
    groups: number;
    enabledPercentage: number;
  };
  
  // Conditional rendering helpers
  renderIfEnabled: (featureKey: string, component: React.ReactNode) => React.ReactNode | null;
  renderIfDisabled: (featureKey: string, component: React.ReactNode) => React.ReactNode | null;
}

/**
 * Custom hook for accessing feature flags in React components
 * 
 * @returns Object with feature flag utilities and helpers
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { isFeatureEnabled, renderIfEnabled } = useFeatureFlags();
 *   
 *   return (
 *     <div>
 *       {renderIfEnabled('google_oauth', <GoogleSignIn />)}
 *       {isFeatureEnabled('debug_mode') && <DebugPanel />}
 *     </div>
 *   );
 * }
 * ```
 */
export function useFeatureFlags(): UseFeatureFlagsReturn {
  // Memoize the feature configuration to prevent unnecessary re-renders
  const memoizedConfig = useMemo(() => featureConfig, []);
  
  // Memoize utility functions
  const utilities = useMemo(() => ({
    isFeatureEnabled: (featureKey: string): boolean => {
      return memoizedConfig.isFeatureEnabled(featureKey);
    },
    
    getFeatureMetadata: (featureKey: string): Record<string, unknown> | undefined => {
      return memoizedConfig.getFeatureMetadata(featureKey);
    },
    
    getFeaturesByGroup: (groupKey: string): FeatureFlag[] => {
      return memoizedConfig.getFeaturesByGroup(groupKey);
    },
    
    getAllFeatures: (): FeatureFlag[] => {
      return memoizedConfig.getAllFeatures();
    },
    
    getGroups: (): FeatureGroup[] => {
      return memoizedConfig.groups;
    },
    
    isAuthProviderEnabled: (provider: 'google' | 'github' | 'amazon' | 'email'): boolean => {
      return isAuthProviderEnabled(provider);
    },
    
    getEnabledAuthProviders: (): string[] => {
      return getEnabledAuthProviders();
    },
    
    getStats: () => {
      return getFeatureStats();
    },
    
    renderIfEnabled: (featureKey: string, component: React.ReactNode): React.ReactNode | null => {
      return memoizedConfig.isFeatureEnabled(featureKey) ? component : null;
    },
    
    renderIfDisabled: (featureKey: string, component: React.ReactNode): React.ReactNode | null => {
      return !memoizedConfig.isFeatureEnabled(featureKey) ? component : null;
    }
  }), [memoizedConfig]);
  
  return utilities;
}

/**
 * Higher-Order Component for conditional feature rendering
 *
 * @param featureKey - The feature flag key to check
 * @param fallback - Optional fallback component when feature is disabled
 * @returns HOC that conditionally renders wrapped component
 *
 * @example
 * ```tsx
 * const ConditionalGoogleSignIn = withFeatureFlag('google_oauth')(GoogleSignIn);
 *
 * // With fallback
 * const ConditionalDebugPanel = withFeatureFlag('debug_mode', <div>Debug disabled</div>)(DebugPanel);
 * ```
 */
export function withFeatureFlag(featureKey: string, fallback?: React.ReactNode) {
  return function <P extends object>(Component: React.ComponentType<P>) {
    const WrappedComponent = (props: P) => {
      const { isFeatureEnabled } = useFeatureFlags();

      if (!isFeatureEnabled(featureKey)) {
        return fallback || null;
      }

      return React.createElement(Component, props);
    };

    return WrappedComponent;
  };
}

/**
 * Hook for authentication provider feature flags specifically
 * Optimized for the signin page and auth-related components
 * 
 * @returns Object with auth provider utilities
 */
export function useAuthProviderFlags() {
  const { isAuthProviderEnabled, getEnabledAuthProviders, getFeaturesByGroup } = useFeatureFlags();
  
  return useMemo(() => ({
    // Individual provider checks
    isGoogleEnabled: isAuthProviderEnabled('google'),
    isGitHubEnabled: isAuthProviderEnabled('github'),
    isAmazonEnabled: isAuthProviderEnabled('amazon'),
    isEmailEnabled: isAuthProviderEnabled('email'),
    
    // Bulk operations
    getEnabledProviders: getEnabledAuthProviders,
    getAllAuthFeatures: () => getFeaturesByGroup('auth_providers'),
    
    // Utility checks
    hasAnyProviderEnabled: () => getEnabledAuthProviders().length > 0,
    getEnabledProviderCount: () => getEnabledAuthProviders().length,
    
    // Component metadata
    getProviderMetadata: (provider: 'google' | 'github' | 'amazon' | 'email') => {
      const authFeatures = getFeaturesByGroup('auth_providers');
      const providerMap = {
        google: 'google_oauth',
        github: 'github_oauth',
        amazon: 'amazon_oauth',
        email: 'email_magic_links'
      };
      
      const feature = authFeatures.find(f => f.key === providerMap[provider]);
      return feature?.metadata;
    }
  }), [isAuthProviderEnabled, getEnabledAuthProviders, getFeaturesByGroup]);
}

/**
 * Development hook for feature flag debugging
 * Only available in development mode
 */
export function useFeatureFlagDebug() {
  const { getAllFeatures, getGroups, getStats } = useFeatureFlags();
  
  return useMemo(() => {
    if (process.env.NODE_ENV !== 'development') {
      return null;
    }
    
    return {
      logAllFeatures: () => {
        console.group('🚩 Feature Flags Debug');
        console.log('Stats:', getStats());
        console.log('Groups:', getGroups());
        console.log('All Features:', getAllFeatures());
        console.groupEnd();
      },
      
      logFeaturesByGroup: (groupKey: string) => {
        const group = getGroups().find(g => g.key === groupKey);
        if (group) {
          console.group(`🚩 Feature Group: ${group.name}`);
          console.log('Description:', group.description);
          console.log('Features:', group.features);
          console.groupEnd();
        }
      },
      
      getEnvironmentVariables: () => {
        const features = getAllFeatures();
        const envVars: Record<string, boolean> = {};
        
        features.forEach(feature => {
          // This is a simplified mapping - in real implementation,
          // we'd need to reverse-engineer the env var names
          const envKey = `FEATURE_${feature.key.toUpperCase()}_ENABLED`;
          envVars[envKey] = feature.enabled;
        });
        
        return envVars;
      }
    };
  }, [getAllFeatures, getGroups, getStats]);
}
