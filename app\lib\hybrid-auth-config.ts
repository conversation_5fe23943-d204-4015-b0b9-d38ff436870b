/**
 * Hybrid Authentication Configuration
 * 
 * Uses the hybrid feature flag system to dynamically configure
 * NextAuth providers based on database and environment settings.
 */

import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { CustomPrismaAdapter } from './prisma-adapter';
import { sendVerificationRequest } from './email';
import { isAuthProviderEnabled } from './feature-config';

const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';
const amazonClientId = process.env.AMAZON_CLIENT_ID || '';
const amazonClientSecret = process.env.AMAZON_CLIENT_SECRET || '';
const resendApiKey = process.env.RESEND_API_KEY || '';
const resendSender = process.env.RESEND_SENDER || '';





// Validation helper function - only validates when providers are actually used
function validateCredentials(provider: string) {
  switch (provider) {
    case 'google':
      if (!googleClientId || !googleClientSecret) {
        console.warn('Missing Google OAuth credentials - Google provider will be disabled');
        return false;
      }
      return true;
    case 'github':
      if (!githubClientId || !githubClientSecret) {
        console.warn('Missing GitHub OAuth credentials - GitHub provider will be disabled');
        return false;
      }
      return true;
    case 'amazon':
      if (!amazonClientId || !amazonClientSecret) {
        console.warn('Missing Amazon OAuth credentials - Amazon provider will be disabled');
        return false;
      }
      return true;
    case 'email':
      if (!resendApiKey || !resendSender) {
        console.warn('Missing Resend email credentials - Email provider will be disabled');
        return false;
      }
      return true;
    default:
      return false;
  }
}

// Export a default auth options instance for backward compatibility
// Note: This will use the current state of feature flags at module load time

// Providers are already imported at the top of the file

// Initialize providers immediately using synchronous feature config
const initializeProviders = () => {
  const providers = [];

  if (isAuthProviderEnabled('google') && validateCredentials('google')) {
    providers.push(GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }));
  }

  if (isAuthProviderEnabled('github') && validateCredentials('github')) {
    providers.push(GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }));
  }

  if (isAuthProviderEnabled('amazon') && validateCredentials('amazon')) {
    providers.push({
      id: 'amazon',
      name: 'Amazon',
      type: 'oauth' as const,
      authorization: {
        url: 'https://www.amazon.com/ap/oa',
        params: { scope: 'profile', response_type: 'code' },
      },
      token: 'https://api.amazon.com/auth/o2/token',
      userinfo: 'https://api.amazon.com/user/profile',
      clientId: amazonClientId,
      clientSecret: amazonClientSecret,
      profile(profile: { user_id: string; name: string; email: string }) {
        return { id: profile.user_id, name: profile.name, email: profile.email };
      },
    });
  }

  if (isAuthProviderEnabled('email') && validateCredentials('email')) {
    providers.push(EmailProvider({
      server: {
        host: 'smtp.resend.com',
        port: 587,
        auth: { user: 'resend', pass: resendApiKey },
      },
      from: resendSender,
      sendVerificationRequest,
      maxAge: 24 * 60 * 60,
    }));
  }

  return providers;
};

export const hybridAuthOptions: NextAuthOptions = {
  adapter: CustomPrismaAdapter(),
  debug: process.env.NODE_ENV === 'development',
  providers: initializeProviders(), // Initialize providers immediately
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60,
    updateAge: 24 * 60 * 60,
  },
  pages: {
    signIn: '/signin',
    error: '/signin',
    verifyRequest: '/passwordless',
  },
  useSecureCookies: process.env.NODE_ENV === 'production',
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.session-token' : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    }
  },
  callbacks: {
    async signIn({ account, profile }) {
      if (account?.type === 'oauth' && profile?.email) {
        try {
          const { prisma } = await import('./prisma');
          const existingUser = await prisma.user.findUnique({
            where: { email: profile.email },
            include: { accounts: true }
          });

          if (existingUser) {
            const existingAccount = existingUser.accounts.find(
              acc => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId
            );

            if (!existingAccount) {
              await prisma.account.create({
                data: {
                  userId: existingUser.id,
                  type: account.type,
                  provider: account.provider,
                  providerAccountId: account.providerAccountId,
                  access_token: account.access_token,
                  token_type: account.token_type,
                  scope: account.scope,
                  refresh_token: account.refresh_token,
                  expires_at: account.expires_at,
                  id_token: account.id_token,
                  session_state: account.session_state,
                }
              });
            }
          }
        } catch (error) {
          console.error('Manual account linking failed:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        token.provider = account?.provider;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.id as string;
        session.user.provider = token.provider as string;
      }
      return session;
    },
    async redirect({ baseUrl }) {
      return `${baseUrl}/dashboard`;
    },
  },
};

/**
 * Get hybrid auth options for server-side session validation
 * This is a synchronous version that uses the pre-initialized providers
 */
export function getHybridAuthOptionsForSession(): NextAuthOptions {
  return hybridAuthOptions;
}

