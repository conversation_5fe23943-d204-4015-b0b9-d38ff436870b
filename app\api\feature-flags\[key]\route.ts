import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import {
  getFeatureFlagByKey,
  updateFeatureFlag,
  deleteFeatureFlag
} from '@/app/lib/feature-flag-db';
import { hybridFeatureConfig } from '@/app/lib/hybrid-feature-config';

interface RouteParams {
  params: Promise<{
    key: string;
  }>;
}

export async function GET(request: Request, { params }: RouteParams) {
  try {
    const { key } = await params;

    if (!key) {
      return NextResponse.json(
        { error: 'Feature flag key is required' },
        { status: 400 }
      );
    }

    // Try to get from database first, then fallback to hybrid system
    const featureFlag = await getFeatureFlagByKey(key);
    
    if (!featureFlag) {
      // Check if it exists in environment config
      const isEnabled = await hybridFeatureConfig.isFeatureEnabled(key);
      const metadata = await hybridFeatureConfig.getFeatureMetadata(key);
      
      if (metadata || isEnabled !== false) {
        // Return environment-based flag info
        return NextResponse.json({
          key,
          enabled: isEnabled,
          metadata,
          source: 'environment',
          message: 'This feature flag is configured via environment variables'
        });
      }
      
      return NextResponse.json(
        { error: 'Feature flag not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(featureFlag);
  } catch (error) {
    const { key } = await params;
    console.error(`Error fetching feature flag ${key}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch feature flag' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(getHybridAuthOptionsForSession());
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { key } = await params;
    const body = await request.json();
    const { name, description, enabled, group, metadata } = body;

    if (!key) {
      return NextResponse.json(
        { error: 'Feature flag key is required' },
        { status: 400 }
      );
    }

    // Check if feature flag exists
    const existingFlag = await getFeatureFlagByKey(key);
    if (!existingFlag) {
      return NextResponse.json(
        { error: 'Feature flag not found' },
        { status: 404 }
      );
    }

    // Update the feature flag
    const updatedFlag = await updateFeatureFlag(key, {
      name,
      description,
      enabled,
      group,
      metadata,
      updatedBy: session.user.email
    });

    // Clear hybrid cache to reflect changes
    await hybridFeatureConfig.refresh();

    return NextResponse.json(updatedFlag);
  } catch (error) {
    const { key } = await params;
    console.error(`Error updating feature flag ${key}:`, error);
    return NextResponse.json(
      { error: 'Failed to update feature flag' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(getHybridAuthOptionsForSession());
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { key } = await params;

    if (!key) {
      return NextResponse.json(
        { error: 'Feature flag key is required' },
        { status: 400 }
      );
    }

    // Check if feature flag exists
    const existingFlag = await getFeatureFlagByKey(key);
    if (!existingFlag) {
      return NextResponse.json(
        { error: 'Feature flag not found' },
        { status: 404 }
      );
    }

    // Delete the feature flag
    await deleteFeatureFlag(key);

    // Clear hybrid cache to reflect changes
    await hybridFeatureConfig.refresh();

    return NextResponse.json(
      { message: 'Feature flag deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    const { key } = await params;
    console.error(`Error deleting feature flag ${key}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete feature flag' },
      { status: 500 }
    );
  }
}
