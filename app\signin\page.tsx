'use client';

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import GoogleSignIn from '@/app/components/GoogleSignIn';
import GitHubSignIn from '@/app/components/GitHubSignIn';
import AmazonSignIn from '@/app/components/AmazonSignIn';
import EmailButton from '@/app/components/EmailButton';
import { AuthStateProvider } from '@/app/contexts/AuthStateContext';
import { useAuthProviderFlags } from '@/app/hooks/useHybridFeatureFlags';
import Link from 'next/link';

export default function SignInPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const {
    isGoogleEnabled,
    isGitHubEnabled,
    isAmazonEnabled,
    isEmailEnabled,
    hasAnyProviderEnabled,
    enabledProviderCount,
    isLoading: featureFlagsLoading,
    error: featureFlagsError
  } = useAuthProviderFlags();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      // Use replace to avoid adding to history
      router.replace('/dashboard');
    }
  }, [status, session, router]);

  // Show loading state if user is authenticated (redirecting)
  if (status === 'authenticated') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  // Show loading state for feature flags loading (temporarily bypass NextAuth loading check)
  if (featureFlagsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading configuration...</p>
        </div>
      </div>
    );
  }

  // Show error state if feature flags failed to load
  if (featureFlagsError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-red-50 border border-red-200 rounded-lg max-w-md mx-auto">
          <div className="text-red-600 mb-2">
            <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-red-800 mb-1">Configuration Error</h3>
          <p className="text-sm text-red-700 mb-4">
            Unable to load authentication configuration. Please try refreshing the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 sm:px-6 lg:px-8 relative flex flex-col justify-center">
      {/* Responsive layout container with conditional classes */}
      <div className="flex flex-col items-center justify-center w-full">
          {/* Header section */}
          <div className="text-center max-w-md w-full mb-8 lg:mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight text-neutral-500 mb-2">
              Sign In to Your Account
            </h2>
            <p className="text-sm sm:text-base text-gray-600 mb-3">
              Choose your preferred sign-in method
            </p>
          </div>

          {/* Authentication options */}
          <AuthStateProvider>
            <div className="max-w-md w-full mb-8 lg:mb-12">
              {hasAnyProviderEnabled ? (
                <>
                  {/* Authentication providers */}
                  <div className={`grid gap-4 sm:gap-6 ${
                    enabledProviderCount === 1
                      ? 'grid-cols-1'
                      : enabledProviderCount === 2
                        ? 'grid-cols-1 sm:grid-cols-2'
                        : 'grid-cols-1 sm:grid-cols-2'
                  }`}>
                    {isGoogleEnabled && <GoogleSignIn />}
                    {isGitHubEnabled && <GitHubSignIn />}
                    {isAmazonEnabled && <AmazonSignIn />}
                    {isEmailEnabled && <EmailButton />}
                  </div>
                </>
              ) : (
                <div className="text-center p-8 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="text-yellow-600 mb-2">
                    <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-yellow-800 mb-1">No Authentication Providers Available</h3>
                  <p className="text-sm text-yellow-700">
                    All authentication providers are currently disabled. Please contact an administrator.
                  </p>
                </div>
              )}
            </div>
          </AuthStateProvider>

        {/* Footer section */}
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
            aria-label="Return to home page"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
