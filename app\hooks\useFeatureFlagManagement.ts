/**
 * React Hook for Feature Flag Management Operations
 * 
 * Provides admin functionality for creating, updating, deleting, and toggling
 * feature flags with optimistic UI updates and error handling.
 */

'use client';

import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

// Types for feature flag operations
interface FeatureFlag {
  id: string;
  key: string;
  name: string;
  description?: string;
  enabled: boolean;
  group: string;
  metadata?: Record<string, unknown>;
  source: 'database' | 'environment';
  createdAt?: Date;
  updatedAt?: Date;
}

interface CreateFeatureFlagInput {
  key: string;
  name: string;
  description?: string;
  enabled?: boolean;
  group: string;
  metadata?: Record<string, unknown>;
}

interface UpdateFeatureFlagInput {
  name?: string;
  description?: string;
  enabled?: boolean;
  group?: string;
  metadata?: Record<string, unknown>;
}

interface UseFeatureFlagManagementReturn {
  // Loading states
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isToggling: boolean;
  isSeeding: boolean;
  
  // Error states
  createError: string | null;
  updateError: string | null;
  deleteError: string | null;
  toggleError: string | null;
  seedError: string | null;
  
  // Operations
  createFeatureFlag: (input: CreateFeatureFlagInput) => Promise<FeatureFlag | null>;
  updateFeatureFlag: (key: string, input: UpdateFeatureFlagInput) => Promise<FeatureFlag | null>;
  deleteFeatureFlag: (key: string) => Promise<boolean>;
  toggleFeatureFlag: (key: string) => Promise<FeatureFlag | null>;
  seedDefaultFlags: () => Promise<boolean>;
  
  // Utility
  clearErrors: () => void;
}

export function useFeatureFlagManagement(): UseFeatureFlagManagementReturn {
  const { data: session } = useSession();
  
  // Loading states
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isToggling, setIsToggling] = useState(false);
  const [isSeeding, setIsSeeding] = useState(false);
  
  // Error states
  const [createError, setCreateError] = useState<string | null>(null);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [toggleError, setToggleError] = useState<string | null>(null);
  const [seedError, setSeedError] = useState<string | null>(null);

  const createFeatureFlag = useCallback(async (input: CreateFeatureFlagInput): Promise<FeatureFlag | null> => {
    if (!session?.user?.email) {
      setCreateError('Authentication required');
      return null;
    }

    setIsCreating(true);
    setCreateError(null);

    try {
      const response = await fetch('/api/feature-flags', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create feature flag');
      }

      const createdFlag: FeatureFlag = await response.json();
      return createdFlag;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create feature flag';
      setCreateError(errorMessage);
      console.error('Error creating feature flag:', error);
      return null;
    } finally {
      setIsCreating(false);
    }
  }, [session]);

  const updateFeatureFlag = useCallback(async (key: string, input: UpdateFeatureFlagInput): Promise<FeatureFlag | null> => {
    if (!session?.user?.email) {
      setUpdateError('Authentication required');
      return null;
    }

    setIsUpdating(true);
    setUpdateError(null);

    try {
      const response = await fetch(`/api/feature-flags/${key}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update feature flag');
      }

      const updatedFlag: FeatureFlag = await response.json();
      return updatedFlag;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update feature flag';
      setUpdateError(errorMessage);
      console.error('Error updating feature flag:', error);
      return null;
    } finally {
      setIsUpdating(false);
    }
  }, [session]);

  const deleteFeatureFlag = useCallback(async (key: string): Promise<boolean> => {
    if (!session?.user?.email) {
      setDeleteError('Authentication required');
      return false;
    }

    setIsDeleting(true);
    setDeleteError(null);

    try {
      const response = await fetch(`/api/feature-flags/${key}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete feature flag');
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete feature flag';
      setDeleteError(errorMessage);
      console.error('Error deleting feature flag:', error);
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [session]);

  const toggleFeatureFlag = useCallback(async (key: string): Promise<FeatureFlag | null> => {
    if (!session?.user?.email) {
      setToggleError('Authentication required');
      return null;
    }

    setIsToggling(true);
    setToggleError(null);

    try {
      const response = await fetch(`/api/feature-flags/${key}/toggle`, {
        method: 'PATCH',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle feature flag');
      }

      const toggledFlag: FeatureFlag = await response.json();
      return toggledFlag;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to toggle feature flag';
      setToggleError(errorMessage);
      console.error('Error toggling feature flag:', error);
      return null;
    } finally {
      setIsToggling(false);
    }
  }, [session]);

  const seedDefaultFlags = useCallback(async (): Promise<boolean> => {
    if (!session?.user?.email) {
      setSeedError('Authentication required');
      return false;
    }

    setIsSeeding(true);
    setSeedError(null);

    try {
      const response = await fetch('/api/feature-flags/seed', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to seed feature flags');
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to seed feature flags';
      setSeedError(errorMessage);
      console.error('Error seeding feature flags:', error);
      return false;
    } finally {
      setIsSeeding(false);
    }
  }, [session]);

  const clearErrors = useCallback(() => {
    setCreateError(null);
    setUpdateError(null);
    setDeleteError(null);
    setToggleError(null);
    setSeedError(null);
  }, []);

  return {
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,
    isToggling,
    isSeeding,
    
    // Error states
    createError,
    updateError,
    deleteError,
    toggleError,
    seedError,
    
    // Operations
    createFeatureFlag,
    updateFeatureFlag,
    deleteFeatureFlag,
    toggleFeatureFlag,
    seedDefaultFlags,
    
    // Utility
    clearErrors
  };
}
