import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getHybridAuthOptionsForSession } from '@/app/lib/hybrid-auth-config';
import { seedDefaultFeatureFlags } from '@/app/lib/feature-flag-db';
import { hybridFeatureConfig } from '@/app/lib/hybrid-feature-config';
import { prisma } from '@/app/lib/prisma';

export async function POST() {
  try {
    // Check authentication
    const session = await getServerSession(getHybridAuthOptionsForSession());
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Find the user ID from the email
    let userId: string | undefined;
    if (session.user.email) {
      const user = await prisma.user.findUnique({
        where: { email: session.user.email }
      });
      userId = user?.id;
    }

    // Seed default feature flags
    const seededFlags = await seedDefaultFeatureFlags(userId);

    // Clear hybrid cache to reflect changes
    await hybridFeatureConfig.refresh();

    return NextResponse.json({
      message: 'Default feature flags seeded successfully',
      seededFlags: seededFlags.length,
      flags: seededFlags.map(flag => ({
        key: flag.key,
        name: flag.name,
        enabled: flag.enabled,
        group: flag.group
      }))
    });
  } catch (error) {
    console.error('Error seeding feature flags:', error);
    return NextResponse.json(
      { error: 'Failed to seed feature flags' },
      { status: 500 }
    );
  }
}
