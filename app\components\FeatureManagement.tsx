/**
 * Feature Management Dashboard Component
 * 
 * Displays the current state of all feature flags in a user-friendly interface.
 * Useful for debugging and understanding which features are currently enabled.
 */

'use client';

import { useFeatureFlags } from '@/app/hooks/useFeatureFlags';
import { FeatureFlag, FeatureGroup } from '@/app/lib/feature-config';

interface FeatureManagementProps {
  className?: string;
  showStats?: boolean;
  showGroupDescriptions?: boolean;
  compactMode?: boolean;
}

export default function FeatureManagement({
  className = '',
  showStats = true,
  showGroupDescriptions = true,
  compactMode = false
}: FeatureManagementProps) {
  const { getGroups, getStats } = useFeatureFlags();
  const groups = getGroups();
  const stats = getStats();

  const getStatusColor = (enabled: boolean) => {
    return enabled 
      ? 'bg-green-100 text-green-800 border-green-200' 
      : 'bg-red-100 text-red-800 border-red-200';
  };

  const getStatusIcon = (enabled: boolean) => {
    return enabled ? (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    ) : (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
    );
  };

  const FeatureItem = ({ feature }: { feature: FeatureFlag }) => (
    <div className={`flex items-center justify-between p-3 bg-white border rounded-lg ${compactMode ? 'p-2' : ''}`}>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <h4 className={`font-medium text-gray-900 ${compactMode ? 'text-sm' : ''}`}>
            {feature.name}
          </h4>
          <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(feature.enabled)}`}>
            {getStatusIcon(feature.enabled)}
            {feature.enabled ? 'Enabled' : 'Disabled'}
          </span>
        </div>
        {!compactMode && (
          <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
        )}
        {feature.metadata && Object.keys(feature.metadata).length > 0 && !compactMode && (
          <div className="mt-2 flex flex-wrap gap-1">
            {Object.entries(feature.metadata).map(([key, value]) => (
              <span key={key} className="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                <span className="font-medium">{key}:</span>
                <span className="ml-1">{String(value)}</span>
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const GroupSection = ({ group }: { group: FeatureGroup }) => {
    const enabledCount = group.features.filter(f => f.enabled).length;
    const totalCount = group.features.length;
    
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className={`font-semibold text-gray-900 ${compactMode ? 'text-base' : 'text-lg'}`}>
              {group.name}
            </h3>
            {showGroupDescriptions && !compactMode && (
              <p className="text-sm text-gray-600 mt-1">{group.description}</p>
            )}
          </div>
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">
              {enabledCount}/{totalCount}
            </div>
            <div className="text-xs text-gray-500">enabled</div>
          </div>
        </div>
        
        <div className={`space-y-2 ${compactMode ? 'space-y-1' : ''}`}>
          {group.features.map((feature) => (
            <FeatureItem key={feature.key} feature={feature} />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className={`font-bold text-gray-900 ${compactMode ? 'text-xl' : 'text-2xl'}`}>
          🚩 Feature Management Dashboard
        </h2>
        <p className="text-gray-600 mt-2">
          Current status of all feature flags in the system
        </p>
      </div>

      {/* Statistics */}
      {showStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Features</div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.enabled}</div>
            <div className="text-sm text-gray-600">Enabled</div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.disabled}</div>
            <div className="text-sm text-gray-600">Disabled</div>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.groups}</div>
            <div className="text-sm text-gray-600">Groups</div>
          </div>
        </div>
      )}

      {/* Feature Groups */}
      <div className={`space-y-4 ${compactMode ? 'space-y-3' : ''}`}>
        {groups.map((group) => (
          <GroupSection key={group.key} group={group} />
        ))}
      </div>

      {/* Environment Variables Info */}
      {!compactMode && process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">💡 Development Info</h3>
          <p className="text-sm text-blue-800 mb-2">
            Feature flags are controlled by environment variables in your <code className="bg-blue-100 px-1 rounded">.env.local</code> file.
          </p>
          <p className="text-sm text-blue-800">
            To modify feature flags, update the corresponding <code className="bg-blue-100 px-1 rounded">FEATURE_*_ENABLED</code> variables and restart the development server.
          </p>
        </div>
      )}

      {/* Footer */}
      <div className="text-center text-sm text-gray-500">
        <p>
          Feature flags are loaded from environment variables at server startup.
          {process.env.NODE_ENV === 'development' && ' Restart the server after making changes.'}
        </p>
      </div>
    </div>
  );
}
